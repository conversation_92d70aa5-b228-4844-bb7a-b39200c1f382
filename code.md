Vou explicar todos os outros opcodes (cases) do protocolo Sigmally que são processados no sistema:

## **Case 0x11: Position Update - Atualização de Posição**

```javascript path=fixes.js mode=EXCERPT
case 0x11: {
    // position update
    vision.camera.tx = dat.getFloat32(o, true);     // Target X da câmera
    vision.camera.ty = dat.getFloat32(o + 4, true); // Target Y da câmera
    vision.camera.tscale = dat.getFloat32(o + 8, true); // Target scale/zoom
    break;
}
```

**Função**: Atualiza a posição e zoom da câmera. Enviado quando o jogador se move ou muda de tamanho.

## **Case 0x12: Leaderboard Update - Atualização do Leaderboard**

```javascript path=fixes.js mode=EXCERPT
case 0x12: {
    // leaderboard update
    vision.leaderboard.length = 0;

    const count = dat.getUint32(o, true);
    o += 4;

    for (let i = 0; i < count; ++i) {
        const me = !!dat.getUint8(o++);        // Se é o próprio jogador
        let name;
        [name, o] = aux.readZTString(dat, o);   // Nome do jogador

        vision.leaderboard.push({
            name: aux.parseName(name),
            me,
            sub: false,  // Subscriber status
            place: undefined,
        });
    }
    break;
}
```

**Função**: Atualiza a lista dos top 10 jogadores no servidor.

## **Case 0x14: Clear Cells - Limpar Células Próprias**

```javascript path=fixes.js mode=EXCERPT
case 0x14: {
    // delete my cells
    // only reset spawn time if no other tab is alive.
    // this could be cheated (if you alternate respawning your tabs, for example) but i don't think
    // multiboxers ever see the stats menu anyway
    if (!world.alive()) world.stats.spawnedAt = undefined;
    ui.deathScreen.hide(); // don't trigger death screen on respawn
    break;
}
```

**Função**: Remove todas as células do jogador (morte ou respawn). Reseta estatísticas se nenhuma aba estiver viva.

## **Case 0x20: Remove Cells - Remover Células Específicas**

```javascript path=fixes.js mode=EXCERPT
case 0x20: {
    // remove cells
    const count = dat.getUint16(o, true);
    o += 2;

    for (let i = 0; i < count; ++i) {
        const id = dat.getUint32(o, true);
        o += 4;

        const record = (world.pellets.get(id) ?? world.cells.get(id))?.views.get(view);
        if (!record) continue;

        const frame = record.frames[0];
        if (frame.deadAt !== undefined) continue;

        // Marca como removida
        record.frames[0] = {
            nx: frame.nx, ny: frame.ny, nr: frame.nr,
            born: frame.born,
            deadAt: now,
            deadTo: -1,  // -1 = removida, não comida
        };
    }
    break;
}
```

**Função**: Remove células específicas que saíram do campo de visão.

## **Case 0x30: Map Borders - Definir Bordas do Mapa**

```javascript path=fixes.js mode=EXCERPT
case 0x30: {
    // map size
    vision.border = {
        l: dat.getFloat64(o, true),      // Left border
        t: dat.getFloat64(o + 8, true),  // Top border
        r: dat.getFloat64(o + 16, true), // Right border
        b: dat.getFloat64(o + 24, true), // Bottom border
    };
    break;
}
```

**Função**: Define os limites do mapa (bordas invisíveis que impedem movimento).

## **Case 0x40: Own Cells - Definir Células Próprias**

```javascript path=fixes.js mode=EXCERPT
case 0x40: {
    // own cells
    vision.owned.clear();

    const count = dat.getUint16(o, true);
    o += 2;

    for (let i = 0; i < count; ++i) {
        const id = dat.getUint32(o, true);
        o += 4;
        vision.owned.add(id);  // Adiciona ID às células próprias
    }

    if (vision.owned.size > 0) {
        vision.spawned = now;  // Marca tempo de spawn
        world.stats.spawnedAt ??= now;
    }
    break;
}
```

**Função**: Informa quais células pertencem ao jogador atual.

## **Case 0x63: Chat Message - Mensagem de Chat**

```javascript path=fixes.js mode=EXCERPT
case 0x63: {
    // chat message
    // only handle non-server chat messages on the primary tab, to prevent duplicate messages
    const flags = dat.getUint8(o++);
    const server = flags & 0x80;  // Se é mensagem do servidor
    if (view !== world.viewId.primary && !server) return; // skip sigmod processing too

    const rgb = [
        dat.getUint8(o++) / 255,  // Red
        dat.getUint8(o++) / 255,  // Green
        dat.getUint8(o++) / 255,  // Blue
        1                         // Alpha
    ];

    let name;
    [name, o] = aux.readZTString(dat, o);  // Nome do remetente
    let msg;
    [msg, o] = aux.readZTString(dat, o);   // Mensagem

    ui.chat.add(name, rgb, msg, !!(flags & 0x80));
    break;
}
```

**Função**: Processa mensagens de chat de jogadores e do servidor.

## **Case 0xb4: Password Error - Erro de Senha**

```javascript path=fixes.js mode=EXCERPT
case 0xb4: {
    // incorrect password alert
    ui.error("Password is incorrect");
    break;
}
```

**Função**: Exibe erro quando senha do servidor privado está incorreta.

## **Case 0x64: Server Stats - Estatísticas do Servidor**

```javascript path=fixes.js mode=EXCERPT
case 0x64: {
    // server stats
    vision.stats = {
        info: aux.readZTString(dat, o)[0],           // Informações do servidor
        gamemode: aux.readZTString(dat, o + 2)[0],   // Modo de jogo
        loadTime: dat.getFloat32(o + 4, true),       // Tempo de carregamento
        uptime: dat.getFloat32(o + 8, true),         // Uptime do servidor
    };
    break;
}
```

**Função**: Recebe estatísticas e informações do servidor.

## **Case 0x65: Party Info - Informações de Party**

```javascript path=fixes.js mode=EXCERPT
case 0x65: {
    // party info
    const partyCode = aux.readZTString(dat, o)[0];
    ui.party.setCode(partyCode);  // Atualiza código da party
    break;
}
```

**Função**: Informa o código da party atual para compartilhamento.

## **Case 0x66: Party Update - Atualização de Party**

```javascript path=fixes.js mode=EXCERPT
case 0x66: {
    // party update
    const isLeader = !!dat.getUint8(o++);  // Se é líder da party
    const memberCount = dat.getUint16(o, true);
    o += 2;

    const members = [];
    for (let i = 0; i < memberCount; ++i) {
        const name = aux.readZTString(dat, o)[0];
        members.push(name);
        o += name.length + 1;
    }

    ui.party.updateMembers(members, isLeader);
    break;
}
```

**Função**: Atualiza lista de membros da party e status de liderança.

## **Case 0x67: Minimap Update - Atualização do Minimapa**

```javascript path=fixes.js mode=EXCERPT
case 0x67: {
    // minimap update
    const count = dat.getUint16(o, true);
    o += 2;

    vision.minimap = [];
    for (let i = 0; i < count; ++i) {
        const x = dat.getFloat32(o, true);
        const y = dat.getFloat32(o + 4, true);
        const size = dat.getUint8(o + 8);
        o += 9;

        vision.minimap.push({ x, y, size });
    }
    break;
}
```

**Função**: Atualiza posições de células grandes no minimapa.

## **Resumo dos Opcodes:**

| Opcode | Nome            | Função Principal                          |
| ------ | --------------- | ----------------------------------------- |
| `0x10` | World Update    | Atualização completa de células e pellets |
| `0x11` | Position Update | Posição e zoom da câmera                  |
| `0x12` | Leaderboard     | Top 10 jogadores                          |
| `0x14` | Clear Cells     | Remove células próprias (morte/respawn)   |
| `0x20` | Remove Cells    | Remove células específicas                |
| `0x30` | Map Borders     | Define limites do mapa                    |
| `0x40` | Own Cells       | Informa células próprias                  |
| `0x63` | Chat Message    | Mensagens de chat                         |
| `0x64` | Server Stats    | Estatísticas do servidor                  |
| `0x65` | Party Info      | Código da party                           |
| `0x66` | Party Update    | Membros da party                          |
| `0x67` | Minimap         | Atualização do minimapa                   |
| `0xb4` | Password Error  | Erro de senha                             |

Cada opcode tem uma estrutura de dados específica e é processado de forma otimizada para manter o desempenho do multiboxing em tempo real.
