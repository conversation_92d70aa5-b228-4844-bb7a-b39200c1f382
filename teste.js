"use strict";
// // ==UserScript==
// // @name         Script Teste
// // @namespace    script-test
// // @version      1.0.0
// // <AUTHOR>
// // @description  Um script de teste para demonstrar funcionalidades avançadas do Tampermonkey
// // @license MIT
// // @match        https://agariobr.com.br/*
// // @grant        none
// // @run-at       document-start
// // ==/UserScript==
// // ===== CONFIGURAÇÕES =====
// const CONFIG = {
// 	SCRIPT_NAME: "teste",
// 	VERSION: "1.0.30",
// 	STORAGE_KEYS: {
// 		SETTINGS: "settings",
// 	},
// } as const;
// export enum NetworkOpcode {
// 	WorldUpdate = 0x10, // Atualização completa de células e pellets
// 	PositionUpdate = 0x11, // Posição e zoom da câmera
// 	Leaderboard = 0x12, // Top 10 jogadores
// 	ClearCells = 0x14, // Remove células próprias (morte/respawn)
// 	RemoveCells = 0x20, // Remove células específicas
// 	MapBorders = 0x30, // Define limites do mapa
// 	OwnCells = 0x40, // Informa células próprias
// 	ChatMessage = 0x63, // Mensagens de chat
// 	ServerStats = 0x64, // Estatísticas do servidor
// 	PartyInfo = 0x65, // Código da party
// 	PartyUpdate = 0x66, // Membros da party
// 	Minimap = 0x67, // Atualização do minimapa
// 	PasswordError = 0xb4, // Erro de senha
// }
// // ===== SERVIÇOS DE ARMAZENAMENTO ESSENCIAL =====
// class StorageService {
// 	static get<T>(key: string): T | null {
// 		try {
// 			const item = localStorage.getItem(key);
// 			return item ? JSON.parse(item) : null;
// 		} catch {
// 			return null;
// 		}
// 	}
// 	static setJSON<T>(key: string, value: T): boolean {
// 		try {
// 			localStorage.setItem(key, JSON.stringify(value));
// 			return true;
// 		} catch {
// 			return false;
// 		}
// 	}
// 	static remove(key: string): boolean {
// 		try {
// 			localStorage.removeItem(key);
// 			return true;
// 		} catch {
// 			return false;
// 		}
// 	}
// }
// // ===== UTILITÁRIOS DOM ESSENCIAL =====
// interface ElementOptions {
// 	className?: string;
// 	textContent?: string;
// 	styles?: Partial<CSSStyleDeclaration>;
// 	attributes?: Record<string, string>;
// 	eventListeners?: Record<string, EventListener>;
// }
// class DOMUtilities {
// 	static createElement<T extends HTMLElement>(tag: string, options: ElementOptions = {}): T {
// 		const element = document.createElement(tag) as T;
// 		if (options.className) element.className = options.className;
// 		if (options.textContent) element.textContent = options.textContent;
// 		if (options.styles) Object.assign(element.style, options.styles);
// 		if (options.attributes)
// 			Object.entries(options.attributes).forEach(([key, value]) => {
// 				element.setAttribute(key, value);
// 			});
// 		if (options.eventListeners)
// 			Object.entries(options.eventListeners).forEach(([event, listener]) => {
// 				element.addEventListener(event, listener);
// 			});
// 		return element;
// 	}
// 	static removeElement(element: HTMLElement | null): boolean {
// 		if (element && element.parentNode) {
// 			element.parentNode.removeChild(element);
// 			return true;
// 		}
// 		return false;
// 	}
// }
// // ===== GERENCIAMENTO DE ESTADO ESSENCIAL =====
// type SettingsValue = string | number | boolean | object | null;
// class SettingsStore {
// 	private static instance: SettingsStore;
// 	private settings: Record<string, SettingsValue> = {};
// 	private readonly storageKey = CONFIG.STORAGE_KEYS.SETTINGS;
// 	private constructor() {
// 		this.loadSettings();
// 	}
// 	static getInstance(): SettingsStore {
// 		if (!SettingsStore.instance) {
// 			SettingsStore.instance = new SettingsStore();
// 		}
// 		return SettingsStore.instance;
// 	}
// 	private loadSettings(): void {
// 		const savedSettings = StorageService.get<Record<string, SettingsValue>>(this.storageKey);
// 		if (savedSettings && typeof savedSettings === "object") {
// 			this.settings = savedSettings;
// 		}
// 	}
// 	private saveSettings(): boolean {
// 		return StorageService.setJSON(this.storageKey, this.settings);
// 	}
// 	getAllSettings(): Record<string, SettingsValue> {
// 		return { ...this.settings };
// 	}
// 	getSetting<T extends SettingsValue>(key: string): T | null {
// 		return (this.settings[key] as T) || null;
// 	}
// 	setSetting(key: string, value: SettingsValue): boolean {
// 		this.settings[key] = value;
// 		return this.saveSettings();
// 	}
// }
// // ===== GERENCIADOR DE KEYBINDS ESSENCIAL =====
// type KeyHandler = (event: KeyboardEvent) => void | boolean;
// interface KeyBinding {
// 	key: string;
// 	ctrl?: boolean;
// 	shift?: boolean;
// 	alt?: boolean;
// 	handler: KeyHandler;
// 	description?: string;
// }
// class KeyBindManager {
// 	private static instance: KeyBindManager;
// 	private keyBindings: Map<string, KeyBinding> = new Map();
// 	private constructor() {
// 		this.setupGlobalListener();
// 	}
// 	static getInstance(): KeyBindManager {
// 		if (!KeyBindManager.instance) {
// 			KeyBindManager.instance = new KeyBindManager();
// 		}
// 		return KeyBindManager.instance;
// 	}
// 	private setupGlobalListener(): void {
// 		document.addEventListener("keydown", event => {
// 			const key = event.key.toLowerCase();
// 			const binding = this.keyBindings.get(key);
// 			if (binding) {
// 				const result = binding.handler(event);
// 				if (result !== false) {
// 					event.preventDefault();
// 					event.stopPropagation();
// 				}
// 			}
// 		});
// 	}
// 	register(binding: KeyBinding): boolean {
// 		const key = binding.key.toLowerCase();
// 		this.keyBindings.set(key, binding);
// 		return true;
// 	}
// 	listBindings(): KeyBinding[] {
// 		return Array.from(this.keyBindings.values());
// 	}
// }
// // ===== INTERFACES DE REDE =====
// // Dados de handshake para cifra/decifra de mensagens
// interface NetworkHandshake {
// 	shuffle: Uint8Array;
// 	unshuffle: Uint8Array;
// }
// // Estado de bloqueio de jogo (entrando/saindo do mundo)
// interface PlayBlockState {
// 	state: "leaving" | "joining";
// 	started: number;
// }
// // Conexão individual para cada visualização
// interface NetworkConnection {
// 	handshake?: NetworkHandshake;
// 	latency?: number;
// 	pinged?: number;
// 	playBlock?: PlayBlockState;
// 	rejections: number;
// 	retries: number;
// 	ws?: WebSocket;
// }
// // ===== GERENCIADOR DE REDE AVANÇADO =====
// class NetworkManager {
// 	private static instance: NetworkManager;
// 	private originalWebSocket: typeof WebSocket;
// 	private readonly textEncoder = new TextEncoder();
// 	private captchaPostQueue = Promise.resolve();
// 	// Armazenamento de conexões por visualização
// 	private readonly connections: Map<symbol, NetworkConnection> = new Map();
// 	// Buffer de mensagens para debug e análise
// 	private messageBuffer: Array<{
// 		timestamp: number;
// 		view: symbol;
// 		data: any;
// 		type: "sent" | "received";
// 		opcode?: number;
// 		decoded?: boolean;
// 	}> = [];
// 	private constructor() {
// 		this.originalWebSocket = window.WebSocket;
// 		this.interceptWebSocket();
// 		this.startPingLoop();
// 		this.startGamemodeMonitoring();
// 		this.startQuestTimers();
// 	}
// 	static getInstance(): NetworkManager {
// 		if (!NetworkManager.instance) {
// 			NetworkManager.instance = new NetworkManager();
// 		}
// 		return NetworkManager.instance;
// 	}
// 	// ===== INTERCEPTAÇÃO DE WEBSOCKET =====
// 	// Intercepta construção de WebSockets para servidores do jogo
// 	private interceptWebSocket(): void {
// 		const self = this;
// 		window.WebSocket = class extends self.originalWebSocket {
// 			constructor(url: string | URL, protocols?: string | string[]) {
// 				super(url, protocols);
// 				const urlString = url.toString();
// 				// Detecta conexões para servidores de jogo
// 				if (urlString.includes("agariobr.com.br") || urlString.includes("sigmally.com")) {
// 					console.log("🔗 Interceptando WebSocket para:", urlString);
// 					self.handleNewConnection(this, urlString);
// 				}
// 			}
// 		};
// 		Object.setPrototypeOf(window.WebSocket.prototype, this.originalWebSocket.prototype);
// 		Object.setPrototypeOf(window.WebSocket, this.originalWebSocket);
// 	}
// 	// Processa nova conexão WebSocket detectada
// 	private handleNewConnection(socket: WebSocket, url: string): void {
// 		console.log("🔗 Nova conexão WebSocket detectada:", url);
// 		// Encontra ou cria uma visualização para esta conexão
// 		const view = this.findOrCreateView();
// 		const connection = this.connections.get(view) || this.createConnection(view);
// 		connection.ws = socket;
// 		this.attachSocketListeners(socket, view);
// 	}
// 	// ===== GERENCIAMENTO DE CONEXÕES =====
// 	// Cria uma nova conexão para uma visualização
// 	createConnection(view: symbol): NetworkConnection {
// 		if (this.connections.has(view)) return this.connections.get(view)!;
// 		const connection: NetworkConnection = {
// 			handshake: undefined,
// 			latency: undefined,
// 			pinged: undefined,
// 			playBlock: undefined,
// 			rejections: 0,
// 			retries: 0,
// 			ws: this.connect(view),
// 		};
// 		this.connections.set(view, connection);
// 		return connection;
// 	}
// 	// Estabelece conexão WebSocket para uma visualização
// 	private connect(view: symbol, establishedCallback?: () => void): WebSocket | undefined {
// 		const connection = this.connections.get(view);
// 		if (connection?.ws) return connection.ws; // já está sendo processada
// 		const realUrl = this.getGameUrl();
// 		// Previne detecção de sigmod usando URL fake
// 		const fakeUrl = { includes: () => false, toString: () => realUrl } as any;
// 		let ws: WebSocket;
// 		try {
// 			ws = new this.originalWebSocket(fakeUrl);
// 		} catch (err) {
// 			console.error("💥 Erro ao criar WebSocket:", err);
// 			this.showConnectionError();
// 			return;
// 		}
// 		// Atualiza referência da conexão
// 		if (connection) connection.ws = ws;
// 		ws.binaryType = "arraybuffer";
// 		this.attachSocketListeners(ws, view, establishedCallback);
// 		return ws;
// 	}
// 	// Adiciona listeners de eventos do WebSocket
// 	private attachSocketListeners(socket: WebSocket, view: symbol, establishedCallback?: () => void): void {
// 		const originalSend = socket.send.bind(socket);
// 		// Intercepta mensagens enviadas
// 		socket.send = (data: string | ArrayBufferLike | Blob | ArrayBufferView) => {
// 			this.logMessage(view, data, "sent");
// 			return originalSend(data);
// 		};
// 		// Processa eventos do WebSocket
// 		socket.addEventListener("open", () => this.handleSocketOpen(socket, view, establishedCallback));
// 		socket.addEventListener("close", event => this.handleSocketClose(event, view, establishedCallback));
// 		socket.addEventListener("error", event => this.handleSocketError(event, view));
// 		socket.addEventListener("message", event => this.handleSocketMessage(event, view));
// 	}
// 	// ===== MANIPULADORES DE EVENTOS =====
// 	// Conexão WebSocket aberta
// 	private handleSocketOpen(socket: WebSocket, view: symbol, establishedCallback?: () => void): void {
// 		console.log("✅ WebSocket conectado:", socket.url);
// 		establishedCallback?.();
// 		const connection = this.connections.get(view);
// 		if (!connection) return socket.close();
// 		// Reset de contadores de erro
// 		connection.rejections = 0;
// 		connection.retries = 0;
// 		// Envia handshake inicial
// 		socket.send(this.textEncoder.encode("SIG 0.0.1\x00"));
// 	}
// 	// Conexão WebSocket fechada
// 	private handleSocketClose(event: CloseEvent, view: symbol, establishedCallback?: () => void): void {
// 		console.warn("❌ WebSocket fechado:", event.code, event.reason);
// 		establishedCallback?.();
// 		const connection = this.connections.get(view);
// 		if (!connection) return;
// 		// Reset do estado da conexão
// 		this.resetConnectionState(connection, view);
// 		// Tenta reconectar com estratégia baseada em captcha
// 		this.attemptReconnection(view, connection);
// 	}
// 	// Erro na conexão WebSocket
// 	private handleSocketError(event: Event, view: symbol): void {
// 		console.error("💥 Erro no WebSocket:", event);
// 	}
// 	// Mensagem recebida via WebSocket
// 	private handleSocketMessage(event: MessageEvent, view: symbol): void {
// 		const connection = this.connections.get(view);
// 		if (!connection) return;
// 		const data = new DataView(event.data);
// 		this.logMessage(view, event.data, "received");
// 		// Processa handshake se ainda não foi estabelecido
// 		if (!connection.handshake) {
// 			this.processHandshake(data, connection);
// 			return;
// 		}
// 		// Decodifica o opcode da mensagem
// 		const originalOpcode = data.getUint8(0);
// 		const opcode = connection.handshake.unshuffle[originalOpcode];
// 		data.setUint8(0, opcode);
// 		// Processa mensagem baseada no opcode
// 		this.processGameMessage(data, opcode, view, connection);
// 	}
// 	// ===== PROCESSAMENTO DE MENSAGENS =====
// 	// Processa handshake inicial do servidor
// 	private processHandshake(data: DataView, connection: NetworkConnection): void {
// 		let offset = 10; // Pula versão "SIG 0.0.1\0"
// 		const shuffle = new Uint8Array(256);
// 		const unshuffle = new Uint8Array(256);
// 		for (let i = 0; i < 256; i++) {
// 			const shuffled = data.getUint8(offset + i);
// 			shuffle[i] = shuffled;
// 			unshuffle[shuffled] = i;
// 		}
// 		connection.handshake = { shuffle, unshuffle };
// 		console.log("🤝 Handshake estabelecido com sucesso");
// 	}
// 	// Processa mensagens do jogo baseadas no opcode
// 	private processGameMessage(data: DataView, opcode: number, view: symbol, connection: NetworkConnection): void {
// 		const now = performance.now();
// 		switch (opcode) {
// 			case NetworkOpcode.WorldUpdate:
// 				console.log("🌍 Atualização do mundo recebida");
// 				this.processWorldUpdate(data, view, now);
// 				break;
// 			case NetworkOpcode.PositionUpdate:
// 				console.log("📍 Atualização de posição recebida");
// 				this.processPositionUpdate(data, view);
// 				break;
// 			case NetworkOpcode.Leaderboard:
// 				console.log("🏆 Leaderboard recebida");
// 				this.processLeaderboard(data, view);
// 				break;
// 			case NetworkOpcode.ClearCells:
// 				console.log("🧹 Limpeza de células recebida");
// 				this.processClearCells(data, view);
// 				break;
// 			case NetworkOpcode.RemoveCells:
// 				console.log("❌ Remoção de células recebida");
// 				this.processRemoveCells(data, view);
// 				break;
// 			case NetworkOpcode.MapBorders:
// 				console.log("🗺️ Limites do mapa recebidos");
// 				this.processMapBorders(data, view);
// 				break;
// 			case NetworkOpcode.OwnCells:
// 				console.log("👤 Células próprias recebidas");
// 				this.processOwnCells(data, view, now);
// 				break;
// 			case NetworkOpcode.ChatMessage:
// 				console.log("💬 Mensagem de chat recebida");
// 				this.processChatMessage(data, view);
// 				break;
// 			case NetworkOpcode.ServerStats:
// 				console.log("📊 Estatísticas do servidor recebidas");
// 				this.processServerStats(data, view, connection, now);
// 				break;
// 			case NetworkOpcode.PasswordError:
// 				console.log("🔒 Erro de senha recebido");
// 				this.processPasswordError();
// 				break;
// 			default:
// 				console.log(`❓ Opcode desconhecido: 0x${opcode.toString(16)}`);
// 		}
// 	}
// 	// ===== PROCESSADORES DE MENSAGENS ESPECÍFICAS =====
// 	private processWorldUpdate(data: DataView, view: symbol, now: number): void {
// 		// Implementação simplificada - processaria células, pellets, etc.
// 		console.log("Processando atualização do mundo...");
// 	}
// 	private processPositionUpdate(data: DataView, view: symbol): void {
// 		const x = data.getFloat32(1, true);
// 		const y = data.getFloat32(5, true);
// 		const scale = data.getFloat32(9, true);
// 		console.log(`Nova posição de câmera: (${x}, ${y}) scale: ${scale}`);
// 	}
// 	private processLeaderboard(data: DataView, view: symbol): void {
// 		console.log("Atualizando leaderboard...");
// 	}
// 	private processClearCells(data: DataView, view: symbol): void {
// 		console.log("Limpando todas as células...");
// 	}
// 	private processRemoveCells(data: DataView, view: symbol): void {
// 		console.log("Removendo células específicas...");
// 	}
// 	private processMapBorders(data: DataView, view: symbol): void {
// 		const left = data.getFloat64(1, true);
// 		const top = data.getFloat64(9, true);
// 		const right = data.getFloat64(17, true);
// 		const bottom = data.getFloat64(25, true);
// 		console.log(`Limites do mapa: L:${left} T:${top} R:${right} B:${bottom}`);
// 	}
// 	private processOwnCells(data: DataView, view: symbol, now: number): void {
// 		const cellId = data.getUint32(1, true);
// 		console.log(`Nova célula própria: ${cellId}`);
// 	}
// 	private processChatMessage(data: DataView, view: symbol): void {
// 		console.log("Nova mensagem de chat recebida");
// 	}
// 	private processServerStats(data: DataView, view: symbol, connection: NetworkConnection, now: number): void {
// 		if (connection.pinged !== undefined) {
// 			connection.latency = now - connection.pinged;
// 			console.log(`🏓 Latência atualizada: ${connection.latency}ms`);
// 		}
// 		connection.pinged = undefined;
// 	}
// 	private processPasswordError(): void {
// 		console.error("🔒 Senha incorreta!");
// 	}
// 	// ===== UTILITÁRIOS E HELPERS =====
// 	// Encontra ou cria uma visualização para nova conexão
// 	private findOrCreateView(): symbol {
// 		// Retorna uma visualização existente ou cria nova
// 		// Por simplicidade, usa sempre a mesma
// 		return Symbol("primary-view");
// 	}
// 	// Obtém URL do servidor de jogo
// 	private getGameUrl(): string {
// 		const gamemode = document.querySelector("#gamemode") as HTMLSelectElement;
// 		const firstGamemode = document.querySelector("#gamemode option") as HTMLOptionElement;
// 		if (location.search.startsWith("?ip=")) {
// 			return location.search.slice("?ip=".length);
// 		}
// 		return "wss://" + (gamemode?.value || firstGamemode?.value || "ca0.sigmally.com/ws/");
// 	}
// 	// Mostra erro de conexão para o usuário
// 	private showConnectionError(): void {
// 		console.error("❌ Erro ao conectar: Verifique o endereço do servidor ou configurações de segurança");
// 	}
// 	// Reset do estado da conexão após desconexão
// 	private resetConnectionState(connection: NetworkConnection, view: symbol): void {
// 		connection.handshake = undefined;
// 		connection.latency = undefined;
// 		connection.pinged = undefined;
// 		connection.playBlock = undefined;
// 		connection.rejections++;
// 		if (connection.retries > 0) connection.retries--;
// 		connection.ws = undefined;
// 	}
// 	// Tentativa de reconexão com estratégia baseada em captcha
// 	private attemptReconnection(view: symbol, connection: NetworkConnection): void {
// 		if (connection.retries > 0) {
// 			setTimeout(() => this.connect(view), 500);
// 		} else {
// 			// Implementaria lógica de captcha aqui
// 			connection.retries = 3;
// 			setTimeout(() => this.connect(view), connection.rejections >= 5 ? 5000 : 500);
// 		}
// 	}
// 	// Registra mensagem no buffer para análise
// 	private logMessage(view: symbol, data: any, type: "sent" | "received"): void {
// 		this.messageBuffer.push({
// 			timestamp: performance.now(),
// 			view,
// 			data,
// 			type,
// 		});
// 		// Limita tamanho do buffer
// 		if (this.messageBuffer.length > 1000) {
// 			this.messageBuffer = this.messageBuffer.slice(-500);
// 		}
// 	}
// 	// ===== LOOPS E TIMERS =====
// 	// Loop de ping para medir latência
// 	private startPingLoop(): void {
// 		setInterval(() => {
// 			for (const connection of this.connections.values()) {
// 				if (!connection.handshake || connection.ws?.readyState !== WebSocket.OPEN) continue;
// 				if (connection.pinged !== undefined) {
// 					connection.latency = -1; // Exibe '????ms'
// 				}
// 				connection.pinged = performance.now();
// 				connection.ws.send(connection.handshake.shuffle.slice(0xfe, 0xfe + 1));
// 			}
// 		}, 2000);
// 	}
// 	// Monitora mudanças de gamemode para desconectar conexões antigas
// 	private startGamemodeMonitoring(): void {
// 		setInterval(() => {
// 			const currentUrl = this.getGameUrl();
// 			for (const connection of this.connections.values()) {
// 				if (!connection.ws) continue;
// 				if (connection.ws.readyState !== WebSocket.CONNECTING && connection.ws.readyState !== WebSocket.OPEN) continue;
// 				if (connection.ws.url === currentUrl) continue;
// 				connection.ws.close();
// 			}
// 		}, 200);
// 	}
// 	// Timers para quests (funcionalidade do jogo)
// 	private startQuestTimers(): void {
// 		setInterval(() => {
// 			for (const view of this.connections.keys()) {
// 				this.sendQuestTime(view);
// 			}
// 		}, 1000);
// 	}
// 	// ===== API PÚBLICA =====
// 	// Envia comando de tempo para quest
// 	sendQuestTime(view: symbol): void {
// 		this.sendOpcode(view, 0xbf);
// 	}
// 	// Envia opcode simples
// 	private sendOpcode(view: symbol, opcode: number): void {
// 		const connection = this.connections.get(view);
// 		if (!connection?.handshake || connection.ws?.readyState !== WebSocket.OPEN) return;
// 		connection.ws.send(connection.handshake.shuffle.slice(opcode, opcode + 1));
// 	}
// 	// Obtém informações de todas as conexões
// 	getAllConnectionsInfo(): Array<{ view: symbol; url?: string; readyState?: number; latency?: number }> {
// 		const info: Array<{ view: symbol; url?: string; readyState?: number; latency?: number }> = [];
// 		for (const [view, connection] of this.connections) {
// 			info.push({
// 				view,
// 				url: connection.ws?.url,
// 				readyState: connection.ws?.readyState,
// 				latency: connection.latency,
// 			});
// 		}
// 		return info;
// 	}
// 	// Obtém mensagens recentes para análise
// 	getRecentMessages(seconds: number = 10): typeof this.messageBuffer {
// 		const cutoff = performance.now() - seconds * 1000;
// 		return this.messageBuffer.filter(msg => msg.timestamp > cutoff);
// 	}
// 	// Obtém estatísticas de rede
// 	getNetworkStats() {
// 		return {
// 			totalConnections: this.connections.size,
// 			activeConnections: Array.from(this.connections.values()).filter(c => c.ws?.readyState === WebSocket.OPEN).length,
// 			totalMessages: this.messageBuffer.length,
// 			averageLatency: this.calculateAverageLatency(),
// 		};
// 	}
// 	// Calcula latência média das conexões ativas
// 	private calculateAverageLatency(): number {
// 		const latencies = Array.from(this.connections.values())
// 			.map(c => c.latency)
// 			.filter(l => l !== undefined && l > 0) as number[];
// 		if (latencies.length === 0) return 0;
// 		return latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
// 	}
// }
// // ===== CLASSES DE TIPOS E INTERFACES =====
// // Tipos para representar diferentes estados de câmera
// interface CameraData {
// 	mass: number;
// 	scale: number;
// 	sumX: number;
// 	sumY: number;
// 	weight: number;
// }
// interface Camera {
// 	x: number;
// 	tx: number;
// 	y: number;
// 	ty: number;
// 	scale: number;
// 	tscale: number;
// 	merged: boolean;
// 	updated: number;
// }
// // Dados de visão para cada aba/visualização
// interface Vision {
// 	border?: any;
// 	camera: Camera;
// 	leaderboard: any[];
// 	owned: Set<number>;
// 	spawned: number;
// 	stats?: any;
// 	used: number;
// }
// // Representa um frame de uma célula em um ponto no tempo
// interface CellFrame {
// 	nx: number; // próxima posição X
// 	ny: number; // próxima posição Y
// 	nr: number; // próximo raio
// 	born: number; // momento de nascimento
// 	deadAt?: number; // momento da morte (se morreu)
// 	deadTo: number; // ID da célula que matou
// 	ox: number; // posição X anterior
// 	oy: number; // posição Y anterior
// 	or: number; // raio anterior
// 	jr: number; // raio para física jelly
// 	a: number; // alfa (opacidade)
// 	updated: number; // última atualização
// }
// // Registro de uma célula com histórico de frames
// interface CellRecord {
// 	frames: CellFrame[];
// }
// // Representação completa de uma célula
// interface Cell {
// 	views: Map<symbol, CellRecord>;
// 	model?: CellFrame;
// 	merged?: CellFrame;
// }
// // Coordenadas interpoladas de uma célula
// interface CellPosition {
// 	x: number;
// 	y: number;
// 	r: number;
// 	jr: number;
// 	a: number;
// }
// // ===== CLASSE PRINCIPAL DO MUNDO DO JOGO =====
// class World {
// 	private static instance: World;
// 	// Armazenamento principal de entidades do jogo
// 	private readonly cells: Map<number, Cell> = new Map();
// 	private readonly pellets: Map<number, Cell> = new Map();
// 	// Símbolos únicos para identificar diferentes abas/visualizações
// 	private readonly multis: symbol[] = Array.from({ length: 8 }, () => Symbol());
// 	// IDs das visualizações principais
// 	private readonly viewId = {
// 		primary: this.multis[0],
// 		secondary: this.multis[1],
// 		spectate: Symbol(),
// 	};
// 	// Visualização atualmente selecionada
// 	private selected: symbol = this.viewId.primary;
// 	// Mapa de todas as visualizações ativas
// 	private readonly views: Map<symbol, Vision> = new Map();
// 	// Estado de sincronização entre abas
// 	private synchronized: boolean = false;
// 	private wasFlawlessSynchronized: boolean = false;
// 	private disagreementAt?: number;
// 	private disagreementStart?: number;
// 	private lastClean: number = performance.now();
// 	// Estatísticas do jogador
// 	private stats = {
// 		foodEaten: 0,
// 		highestPosition: 200,
// 		highestScore: 0,
// 		spawnedAt: undefined as number | undefined,
// 	};
// 	// Configurações simuladas (normalmente viriam de um objeto settings global)
// 	private readonly settings = {
// 		cameraMovement: "default",
// 		camera: "default",
// 		multibox: true,
// 		nbox: false,
// 		mergeCamera: true,
// 		cameraSpawnAnimation: true,
// 		cameraSmoothness: 10,
// 		autoZoom: true,
// 		synchronization: "flawless",
// 		drawDelay: 120,
// 		slowerJellyPhysics: false,
// 	};
// 	private constructor() {}
// 	// Padrão Singleton para garantir uma única instância
// 	static getInstance(): World {
// 		if (!World.instance) {
// 			World.instance = new World();
// 		}
// 		return World.instance;
// 	}
// 	// ===== MÉTODOS PÚBLICOS =====
// 	// Verifica se há células vivas em qualquer visualização
// 	isAlive(): boolean {
// 		for (const [view, vision] of this.views) {
// 			for (const id of vision.owned) {
// 				const cell = this.cells.get(id);
// 				// Se a célula não existe ainda, consideramos como viva
// 				if (!cell) return true;
// 				const frame = cell.views.get(view)?.frames[0];
// 				if (frame?.deadAt === undefined) return true;
// 			}
// 		}
// 		return false;
// 	}
// 	// Calcula a câmera para uma visualização específica
// 	calculateSingleCamera(view: symbol, vision?: Vision, weightExponent: number = 0, now: number = performance.now()): CameraData {
// 		vision = vision ?? this.views.get(view);
// 		if (!vision) {
// 			return { mass: 0, scale: 1, sumX: 0, sumY: 0, weight: 0 };
// 		}
// 		let mass = 0;
// 		let r = 0;
// 		let sumX = 0;
// 		let sumY = 0;
// 		let weight = 0;
// 		// Processa todas as células possuídas por esta visualização
// 		for (const id of vision.owned) {
// 			const cell = this.cells.get(id);
// 			const frame = this.synchronized ? cell?.merged : cell?.views.get(view)?.frames[0];
// 			const interp = this.synchronized ? cell?.merged : cell?.views.get(view)?.frames[0];
// 			if (!frame || !interp) continue;
// 			// Não incluir células possuídas antes do respawn
// 			if (frame.born < vision.spawned) continue;
// 			if (this.settings.cameraMovement === "instant") {
// 				const xyr = this.calculatePosition(frame, interp, undefined, undefined, false, now);
// 				r += xyr.r * xyr.a;
// 				mass += ((xyr.r * xyr.r) / 100) * xyr.a;
// 				const cellWeight = xyr.a * Math.pow(xyr.r, weightExponent);
// 				sumX += xyr.x * cellWeight;
// 				sumY += xyr.y * cellWeight;
// 				weight += cellWeight;
// 			} else {
// 				// Movimento de câmera padrão
// 				if (frame.deadAt !== undefined) continue;
// 				const xyr = this.calculatePosition(frame, interp, undefined, undefined, false, now);
// 				r += frame.nr;
// 				mass += (frame.nr * frame.nr) / 100;
// 				const cellWeight = Math.pow(frame.nr, weightExponent);
// 				sumX += xyr.x * cellWeight;
// 				sumY += xyr.y * cellWeight;
// 				weight += cellWeight;
// 			}
// 		}
// 		const scale = Math.pow(Math.min(64 / r, 1), 0.4);
// 		return { mass, scale, sumX, sumY, weight };
// 	}
// 	// Calcula e atualiza as posições de câmera para todas as visualizações
// 	updateCameras(now: number = performance.now()): void {
// 		const weightExponent = this.settings.camera !== "default" ? 2 : 0;
// 		// Cria conjuntos disjuntos de todas as câmeras próximas
// 		const cameras = new Map<symbol, CameraData>();
// 		const sets = new Map<symbol, Set<symbol>>();
// 		for (const [view, vision] of this.views) {
// 			cameras.set(view, this.calculateSingleCamera(view, vision, weightExponent, now));
// 			sets.set(view, new Set([view]));
// 		}
// 		// Calcula mesmo se as abas não forem realmente mescladas (para contornos multi)
// 		if (this.settings.multibox || this.settings.nbox) {
// 			this.mergeCameraSets(cameras, sets, now);
// 		}
// 		// Calcula e atualiza posições de câmera mescladas
// 		this.updateMergedCameras(cameras, sets, now);
// 	}
// 	// Cria ou retorna uma visualização existente
// 	createVision(view: symbol): Vision {
// 		const existing = this.views.get(view);
// 		if (existing) return existing;
// 		const vision: Vision = {
// 			border: undefined,
// 			camera: {
// 				x: 0,
// 				tx: 0,
// 				y: 0,
// 				ty: 0,
// 				scale: 0,
// 				tscale: 0,
// 				merged: false,
// 				updated: performance.now() - 1,
// 			},
// 			leaderboard: [],
// 			owned: new Set(),
// 			spawned: -Infinity,
// 			stats: undefined,
// 			used: -Infinity,
// 		};
// 		this.views.set(view, vision);
// 		return vision;
// 	}
// 	// Sincroniza frames entre diferentes visualizações
// 	synchronizeViews(): void {
// 		if (this.wasFlawlessSynchronized && this.settings.synchronization !== "flawless") this.cleanupFrameHistory();
// 		if (!this.settings.synchronization || this.views.size <= 1) {
// 			this.resetSynchronization();
// 			return;
// 		}
// 		const now = performance.now();
// 		const indices: Record<string | symbol, number> = {};
// 		if (this.settings.synchronization === "flawless") {
// 			this.performFlawlessSynchronization(indices, now);
// 		} else {
// 			this.performLatestSynchronization(indices);
// 		}
// 		this.mergeFrames(indices, now);
// 		this.cleanupFrameHistory();
// 		this.updateSynchronizationState(now);
// 	}
// 	// Calcula pontuação total para uma visualização
// 	calculateScore(view: symbol): number {
// 		let score = 0;
// 		const vision = this.views.get(view);
// 		if (!vision) return 0;
// 		for (const id of vision.owned) {
// 			const cell = this.cells.get(id);
// 			if (!cell) continue;
// 			const frame = this.synchronized ? cell.merged : cell.views.get(view)?.frames[0];
// 			if (!frame || frame.deadAt !== undefined) continue;
// 			// Usa pontuação exata do servidor, sem interpolação
// 			score += (frame.nr * frame.nr) / 100;
// 		}
// 		return score;
// 	}
// 	// Calcula posição interpolada de uma célula
// 	calculatePosition(
// 		frame: CellFrame,
// 		interp: CellFrame,
// 		killerFrame?: CellFrame,
// 		killerInterp?: CellFrame,
// 		isPellet: boolean = false,
// 		now: number = performance.now()
// 	): CellPosition {
// 		let nx = frame.nx;
// 		let ny = frame.ny;
// 		// Anima em direção à posição interpolada do assassino para suavidade extra
// 		if (killerFrame && killerInterp) {
// 			const killerXyr = this.calculatePosition(killerFrame, killerInterp, undefined, undefined, false, now);
// 			nx = killerXyr.x;
// 			ny = killerXyr.y;
// 		}
// 		let x: number, y: number, r: number, a: number;
// 		if (isPellet && frame.deadAt === undefined) {
// 			// Pellets não se movem suavemente
// 			x = nx;
// 			y = ny;
// 			r = frame.nr;
// 			a = 1;
// 		} else {
// 			// Interpolação suave para células
// 			let alpha = (now - interp.updated) / this.settings.drawDelay;
// 			alpha = Math.max(0, Math.min(1, alpha));
// 			x = interp.ox + (nx - interp.ox) * alpha;
// 			y = interp.oy + (ny - interp.oy) * alpha;
// 			r = interp.or + (frame.nr - interp.or) * alpha;
// 			const targetA = frame.deadAt !== undefined ? 0 : 1;
// 			a = interp.a + (targetA - interp.a) * alpha;
// 		}
// 		const dt = (now - interp.updated) / 1000;
// 		const jellyPhysicsSpeed = this.settings.slowerJellyPhysics ? 10 : 5;
// 		return {
// 			x,
// 			y,
// 			r,
// 			jr: this.exponentialEase(interp.jr, r, jellyPhysicsSpeed, dt),
// 			a,
// 		};
// 	}
// 	// Remove células mortas e invisíveis
// 	cleanupDeadCells(): void {
// 		const now = performance.now();
// 		if (now - this.lastClean < 200) return;
// 		this.lastClean = now;
// 		for (const collection of [this.cells, this.pellets]) {
// 			for (const [id, cell] of collection) {
// 				for (const [view, record] of cell.views) {
// 					const firstFrame = record.frames[0];
// 					const lastFrame = record.frames[record.frames.length - 1];
// 					if (firstFrame.deadAt !== lastFrame.deadAt) continue;
// 					if (lastFrame.deadAt !== undefined && now - lastFrame.deadAt >= this.settings.drawDelay + 200) {
// 						cell.views.delete(view);
// 					}
// 				}
// 				if (cell.views.size === 0) {
// 					collection.delete(id);
// 				}
// 			}
// 		}
// 	}
// 	// ===== MÉTODOS PRIVADOS =====
// 	// Mescla conjuntos de câmeras próximas
// 	private mergeCameraSets(cameras: Map<symbol, CameraData>, sets: Map<symbol, Set<symbol>>, now: number): void {
// 		for (const [view, vision] of this.views) {
// 			const set = sets.get(view)!;
// 			const camera = cameras.get(view)!;
// 			if (camera.weight <= 0 || now - vision.used > 20000) continue; // Não mesclar abas inativas
// 			const x = camera.sumX / camera.weight;
// 			const y = camera.sumY / camera.weight;
// 			const width = 1920 / 2 / camera.scale;
// 			const height = 1080 / 2 / camera.scale;
// 			for (const [otherView, otherVision] of this.views) {
// 				const otherSet = sets.get(otherView)!;
// 				if (set === otherSet || now - otherVision.used > 20000) continue;
// 				const otherCamera = cameras.get(otherView)!;
// 				if (otherCamera.weight <= 0) continue;
// 				const otherX = otherCamera.sumX / otherCamera.weight;
// 				const otherY = otherCamera.sumY / otherCamera.weight;
// 				const otherWidth = 1920 / 2 / otherCamera.scale;
// 				const otherHeight = 1080 / 2 / otherCamera.scale;
// 				// Limite de proximidade baseado na massa de ambas as abas
// 				const threshold = 1000 + Math.min(camera.weight / 100 / 25, otherCamera.weight / 100 / 25);
// 				if (Math.abs(x - otherX) <= width + otherWidth + threshold && Math.abs(y - otherY) <= height + otherHeight + threshold) {
// 					// Mesclar conjuntos disjuntos
// 					for (const connectedView of otherSet) {
// 						set.add(connectedView);
// 						sets.set(connectedView, set);
// 					}
// 				}
// 			}
// 		}
// 	}
// 	// Atualiza câmeras mescladas
// 	private updateMergedCameras(cameras: Map<symbol, CameraData>, sets: Map<symbol, Set<symbol>>, now: number): void {
// 		const computed = new Set<Set<symbol>>();
// 		for (const set of sets.values()) {
// 			if (computed.has(set)) continue;
// 			let mass = 0;
// 			let sumX = 0;
// 			let sumY = 0;
// 			let weight = 0;
// 			if (this.settings.mergeCamera) {
// 				for (const view of set) {
// 					const camera = cameras.get(view)!;
// 					mass += camera.mass;
// 					sumX += camera.sumX;
// 					sumY += camera.sumY;
// 					weight += camera.weight;
// 				}
// 			}
// 			for (const view of set) {
// 				const vision = this.views.get(view)!;
// 				if (!this.settings.mergeCamera) {
// 					const camera = cameras.get(view)!;
// 					({ mass, sumX, sumY, weight } = camera);
// 				}
// 				this.updateSingleCameraPosition(vision, mass, sumX, sumY, weight, set.size > 1, now);
// 			}
// 			computed.add(set);
// 		}
// 	}
// 	// Atualiza posição de uma única câmera
// 	private updateSingleCameraPosition(
// 		vision: Vision,
// 		mass: number,
// 		sumX: number,
// 		sumY: number,
// 		weight: number,
// 		isMerged: boolean,
// 		now: number
// 	): void {
// 		let xyFactor: number;
// 		if (weight <= 0) {
// 			xyFactor = 20;
// 		} else if (this.settings.cameraMovement === "instant") {
// 			xyFactor = 1;
// 		} else {
// 			// Movimento de câmera suave após spawnar
// 			const aliveFor = (performance.now() - vision.spawned) / 1000;
// 			const a = Math.max(0, Math.min(1, (aliveFor - 0.3) / 0.3));
// 			const base = this.settings.cameraSpawnAnimation ? 2 : 1;
// 			xyFactor = Math.min(this.settings.cameraSmoothness, base * (1 - a) + this.settings.cameraSmoothness * a);
// 		}
// 		if (weight > 0) {
// 			vision.camera.tx = sumX / weight;
// 			vision.camera.ty = sumY / weight;
// 			let scale: number;
// 			if (this.settings.camera === "default") {
// 				scale = Math.pow(Math.min(64 / Math.sqrt(mass), 1), 0.4);
// 			} else {
// 				scale = Math.pow(Math.min(64 / Math.sqrt(100 * mass), 1), 0.4);
// 			}
// 			vision.camera.tscale = this.settings.autoZoom ? scale : 0.25;
// 		}
// 		const dt = (now - vision.camera.updated) / 1000;
// 		vision.camera.x = this.exponentialEase(vision.camera.x, vision.camera.tx, xyFactor, dt);
// 		vision.camera.y = this.exponentialEase(vision.camera.y, vision.camera.ty, xyFactor, dt);
// 		vision.camera.scale = this.exponentialEase(vision.camera.scale, vision.camera.tscale, 9, dt);
// 		vision.camera.merged = isMerged;
// 		vision.camera.updated = now;
// 	}
// 	// Implementa sincronização perfeita entre visualizações (versão simplificada)
// 	private performFlawlessSynchronization(indices: Record<string | symbol, number>, now: number): void {
// 		// Versão simplificada da sincronização perfeita
// 		// Em uma implementação real, isso envolveria algoritmos complexos de grafos bipartidos
// 		let i = 0;
// 		for (const view of this.views.keys()) {
// 			indices[i++] = indices[view] = 0; // Por simplicidade, usar sempre índice 0
// 		}
// 		this.wasFlawlessSynchronized = true;
// 	}
// 	// Implementa sincronização usando frames mais recentes
// 	private performLatestSynchronization(indices: Record<string | symbol, number>): void {
// 		let i = 0;
// 		for (const view of this.views.keys()) {
// 			indices[i++] = indices[view] = 0;
// 		}
// 		this.wasFlawlessSynchronized = false;
// 	}
// 	// Mescla frames baseado nos índices encontrados
// 	private mergeFrames(indices: Record<string | symbol, number>, now: number): void {
// 		for (const collection of [this.cells, this.pellets]) {
// 			for (const cell of collection.values()) {
// 				// Encontra frame modelo (versão simplificada)
// 				let modelFrame: CellFrame | undefined;
// 				for (const [view, record] of cell.views) {
// 					const frame = record.frames[indices[view] || 0];
// 					if (frame && !modelFrame) {
// 						modelFrame = frame;
// 					}
// 				}
// 				if (modelFrame) {
// 					cell.model = modelFrame;
// 				}
// 			}
// 		}
// 		// Atualiza frames mesclados
// 		this.updateMergedFrames(now);
// 	}
// 	// Atualiza frames mesclados para todas as células
// 	private updateMergedFrames(now: number): void {
// 		for (const collection of [this.cells, this.pellets]) {
// 			for (const cell of collection.values()) {
// 				const { model, merged } = cell;
// 				if (!model) {
// 					cell.merged = undefined;
// 					continue;
// 				}
// 				if (!merged || (merged.deadAt !== undefined && model.deadAt === undefined)) {
// 					// Cria novo frame mesclado
// 					cell.merged = {
// 						nx: model.nx,
// 						ny: model.ny,
// 						nr: model.nr,
// 						born: now,
// 						deadAt: model.deadAt !== undefined ? now : undefined,
// 						deadTo: model.deadTo,
// 						ox: model.nx,
// 						oy: model.ny,
// 						or: model.nr,
// 						jr: model.nr,
// 						a: 0,
// 						updated: now,
// 					};
// 				} else {
// 					// Atualiza frame mesclado existente
// 					if (
// 						merged.deadAt === undefined &&
// 						(model.deadAt !== undefined || model.nx !== merged.nx || model.ny !== merged.ny || model.nr !== merged.nr)
// 					) {
// 						const isPellet = collection === this.pellets;
// 						const xyr = this.calculatePosition(merged, merged, undefined, undefined, isPellet, now);
// 						merged.ox = xyr.x;
// 						merged.oy = xyr.y;
// 						merged.or = xyr.r;
// 						merged.jr = xyr.jr;
// 						merged.a = xyr.a;
// 						merged.updated = now;
// 					}
// 					merged.nx = model.nx;
// 					merged.ny = model.ny;
// 					merged.nr = model.nr;
// 					merged.deadAt = model.deadAt !== undefined ? merged.deadAt ?? now : undefined;
// 					merged.deadTo = model.deadTo;
// 				}
// 			}
// 		}
// 	}
// 	// Remove histórico de frames
// 	private cleanupFrameHistory(): void {
// 		for (const collection of [this.cells, this.pellets]) {
// 			for (const cell of collection.values()) {
// 				for (const record of cell.views.values()) {
// 					// Remove frames antigos, mantendo apenas o atual
// 					while (record.frames.length > 1) {
// 						record.frames.pop();
// 					}
// 				}
// 			}
// 		}
// 	}
// 	// Reset do estado de sincronização
// 	private resetSynchronization(): void {
// 		this.disagreementStart = undefined;
// 		this.disagreementAt = undefined;
// 		this.synchronized = false;
// 		this.wasFlawlessSynchronized = false;
// 	}
// 	// Atualiza estado de sincronização
// 	private updateSynchronizationState(now: number): void {
// 		this.disagreementStart = undefined;
// 		// Se houve desacordo, espera um tempo antes de reativar sincronização
// 		if (this.disagreementAt === undefined || now - this.disagreementAt > 1000) {
// 			this.synchronized = true;
// 		}
// 	}
// 	// Utilitário para easing exponencial
// 	private exponentialEase(current: number, target: number, factor: number, deltaTime: number): number {
// 		return current + (target - current) * (1 - Math.exp(-factor * deltaTime));
// 	}
// 	// ===== GETTERS PÚBLICOS =====
// 	get gameStats() {
// 		return { ...this.stats };
// 	}
// 	get currentView(): symbol {
// 		return this.selected;
// 	}
// 	get allViews(): ReadonlyMap<symbol, Vision> {
// 		return this.views;
// 	}
// 	get isSynchronized(): boolean {
// 		return this.synchronized;
// 	}
// 	get cellCount(): number {
// 		return this.cells.size;
// 	}
// 	get pelletCount(): number {
// 		return this.pellets.size;
// 	}
// }
// // ===== GERENCIADOR DE UI ESSENCIAL =====
// interface UIComponent {
// 	render(): HTMLElement;
// 	destroy(): void;
// }
// class UIManager {
// 	private static instance: UIManager;
// 	private readonly keyBindManager: KeyBindManager;
// 	private isInitialized = false;
// 	private constructor() {
// 		this.keyBindManager = KeyBindManager.getInstance();
// 	}
// 	static getInstance(): UIManager {
// 		if (!UIManager.instance) UIManager.instance = new UIManager();
// 		return UIManager.instance;
// 	}
// 	initialize(): boolean {
// 		if (this.isInitialized) return true;
// 		this.keyBindManager.register({
// 			key: "F1",
// 			handler: () => alert("Ajuda"),
// 			description: "Mostra a ajuda",
// 		});
// 		// Keybind para mostrar status do WebSocket
// 		this.keyBindManager.register({
// 			key: "F2",
// 			handler: () => {
// 				const networkManager = NetworkManager.getInstance();
// 				const connections = networkManager.getAllConnectionsInfo();
// 				console.log("🔍 Informações das conexões:", connections);
// 				const recent = networkManager.getRecentMessages(30);
// 				console.log("🕒 Mensagens dos últimos 30 segundos:", recent);
// 				const stats = networkManager.getNetworkStats();
// 				console.log("📊 Estatísticas de rede:", stats);
// 			},
// 			description: "Mostra informações da rede",
// 		});
// 		this.isInitialized = true;
// 		return true;
// 	}
// 	destroy(): void {
// 		this.isInitialized = false;
// 	}
// }
// // ===== APLICAÇÃO PRINCIPAL ESSENCIAL =====
// class ScriptApplication {
// 	private static instance: ScriptApplication;
// 	private readonly settingsStore: SettingsStore;
// 	private readonly uiManager: UIManager;
// 	private readonly networkManager: NetworkManager;
// 	private readonly world: World;
// 	private isInitialized = false;
// 	private constructor() {
// 		this.settingsStore = SettingsStore.getInstance();
// 		this.world = World.getInstance();
// 		this.uiManager = UIManager.getInstance();
// 		this.networkManager = NetworkManager.getInstance();
// 	}
// 	static getInstance(): ScriptApplication {
// 		if (!ScriptApplication.instance) ScriptApplication.instance = new ScriptApplication();
// 		return ScriptApplication.instance;
// 	}
// 	async initialize(): Promise<boolean> {
// 		if (this.isInitialized) return true;
// 		if (document.readyState === "loading") {
// 			await new Promise(resolve => document.addEventListener("DOMContentLoaded", resolve));
// 		}
// 		this.uiManager.initialize();
// 		this.settingsStore.setSetting("appVersion", CONFIG.VERSION);
// 		console.log("🚀 Script inicializado com gerenciamento avançado de rede");
// 		console.log("ℹ️  Pressione F2 para ver estatísticas de rede");
// 		console.log("🌐 NetworkManager ativo com suporte a múltiplas conexões");
// 		this.isInitialized = true;
// 		return true;
// 	}
// }
// // ===== INICIALIZAÇÃO AUTOMÁTICA =====
// // // Instancia o mundo do jogo como singleton
// // const world = World.getInstance();
// // Inicializa a aplicação principal
// ScriptApplication.getInstance().initialize();
