// ==UserScript==
// @name         Script Teste
// @namespace    script-test
// @version      1.0.0
// <AUTHOR>
// @description  Um script de teste para demonstrar funcionalidades avançadas do Tampermonkey
// @license MIT
// @match        https://agariobr.com.br/*
// @grant        none
// @run-at       document-start
// ==/UserScript==
var ScriptTeste = (function (exports) {
	"use strict";

	// ==UserScript==
	// @name         Script Teste
	// @namespace    script-test
	// @version      1.0.0
	// <AUTHOR>
	// @description  Um script de teste para demonstrar funcionalidades avançadas do Tampermonkey
	// @license MIT
	// @match        https://agariobr.com.br/*
	// @grant        none
	// @run-at       document-start
	// ==/UserScript==
	// ===== CONFIGURAÇÕES =====
	const CONFIG = {
		SCRIPT_NAME: "teste",
		VERSION: "1.0.30",
		STORAGE_KEYS: {
			SETTINGS: "settings",
		},
		NETWORK: {
			WEBSOCKET_URL: "servers.agariobr.com.br:4409",
		},
		DEFAULT_SETTINGS: {
			cameraMovement: "default",
			camera: "default",
			multibox: true,
			nbox: false,
			mergeCamera: true,
			cameraSpawnAnimation: true,
			cameraSmoothness: 10,
			autoZoom: true,
			synchronization: "flawless",
			drawDelay: 120,
			slowerJellyPhysics: false,
		},
	};
	exports.NetworkOpcodeEnum = void 0;
	(function (NetworkOpcodeEnum) {
		NetworkOpcodeEnum[(NetworkOpcodeEnum["WorldUpdate"] = 16)] = "WorldUpdate";
		NetworkOpcodeEnum[(NetworkOpcodeEnum["PositionUpdate"] = 17)] = "PositionUpdate";
		NetworkOpcodeEnum[(NetworkOpcodeEnum["Leaderboard"] = 18)] = "Leaderboard";
		NetworkOpcodeEnum[(NetworkOpcodeEnum["ClearCells"] = 20)] = "ClearCells";
		NetworkOpcodeEnum[(NetworkOpcodeEnum["RemoveCells"] = 32)] = "RemoveCells";
		NetworkOpcodeEnum[(NetworkOpcodeEnum["MapBorders"] = 48)] = "MapBorders";
		NetworkOpcodeEnum[(NetworkOpcodeEnum["OwnCells"] = 64)] = "OwnCells";
		NetworkOpcodeEnum[(NetworkOpcodeEnum["ChatMessage"] = 99)] = "ChatMessage";
		NetworkOpcodeEnum[(NetworkOpcodeEnum["ServerStats"] = 100)] = "ServerStats";
		NetworkOpcodeEnum[(NetworkOpcodeEnum["PartyInfo"] = 101)] = "PartyInfo";
		NetworkOpcodeEnum[(NetworkOpcodeEnum["PartyUpdate"] = 102)] = "PartyUpdate";
		NetworkOpcodeEnum[(NetworkOpcodeEnum["Minimap"] = 103)] = "Minimap";
		NetworkOpcodeEnum[(NetworkOpcodeEnum["PasswordError"] = 180)] = "PasswordError";
	})(exports.NetworkOpcodeEnum || (exports.NetworkOpcodeEnum = {}));
	// ===== SERVIÇOS DE ARMAZENAMENTO ESSENCIAL =====
	class StorageService {
		static get(key) {
			try {
				const item = localStorage.getItem(key);
				return item ? JSON.parse(item) : null;
			} catch (_a) {
				return null;
			}
		}
		static setJSON(key, value) {
			try {
				localStorage.setItem(key, JSON.stringify(value));
				return true;
			} catch (_a) {
				return false;
			}
		}
		static remove(key) {
			try {
				localStorage.removeItem(key);
				return true;
			} catch (_a) {
				return false;
			}
		}
	}
	class SettingsStore {
		constructor() {
			this.settings = {};
			this.storageKey = CONFIG.STORAGE_KEYS.SETTINGS;
			this.loadSettings();
		}
		static getInstance() {
			if (!SettingsStore.instance) {
				SettingsStore.instance = new SettingsStore();
			}
			return SettingsStore.instance;
		}
		loadSettings() {
			const savedSettings = StorageService.get(this.storageKey);
			if (savedSettings && typeof savedSettings === "object") {
				this.settings = savedSettings;
			}
		}
		saveSettings() {
			return StorageService.setJSON(this.storageKey, this.settings);
		}
		getAllSettings() {
			return Object.assign({}, this.settings);
		}
		getSetting(key) {
			return this.settings[key] || null;
		}
		setSetting(key, value) {
			this.settings[key] = value;
			return this.saveSettings();
		}
	}
	class KeyBindManager {
		constructor() {
			this.keyBindings = new Map();
			this.setupGlobalListener();
		}
		static getInstance() {
			if (!KeyBindManager.instance) {
				KeyBindManager.instance = new KeyBindManager();
			}
			return KeyBindManager.instance;
		}
		setupGlobalListener() {
			document.addEventListener("keydown", event => {
				const key = event.key.toLowerCase();
				const binding = this.keyBindings.get(key);
				if (binding) {
					const result = binding.handler(event);
					if (result !== false) {
						event.preventDefault();
						event.stopPropagation();
					}
				}
			});
		}
		register(binding) {
			const key = binding.key.toLowerCase();
			this.keyBindings.set(key, binding);
			return true;
		}
		listBindings() {
			return Array.from(this.keyBindings.values());
		}
	}
	// ===== INTERCEPTADOR DE WEBSOCKET =====
	class WebSocketInterceptor {
		constructor() {
			this.textEncoder = new TextEncoder();
			// Gerenciamento do estado da conexão
			this.connections = new Map();
			this.messageLog = [];
			this.originalWebSocket = window.WebSocket;
			this.setupWebSocketInterception();
			this.startPeriodicTasks();
		}
		// ===== PADRÃO SINGLETON =====
		static getInstance() {
			if (!WebSocketInterceptor.instance) {
				WebSocketInterceptor.instance = new WebSocketInterceptor();
			}
			return WebSocketInterceptor.instance;
		}
		// ===== CONFIGURAÇÃO DA INTERCEPTAÇÃO DO WEBSOCKET =====
		/** Intercepta o construtor do WebSocket para monitorar conexões com o servidor alvo */
		setupWebSocketInterception() {
			const self = this;
			window.WebSocket = class extends self.originalWebSocket {
				constructor(url, protocols) {
					super(url, protocols);
					const urlString = url.toString();
					if (urlString.includes(CONFIG.NETWORK.WEBSOCKET_URL)) {
						self.handleNewConnection(this, urlString);
					}
				}
			};
			// Preserva a cadeia de protótipos original do WebSocket
			Object.setPrototypeOf(window.WebSocket.prototype, this.originalWebSocket.prototype);
			Object.setPrototypeOf(window.WebSocket, this.originalWebSocket);
		}
		/** Lida com a detecção de uma nova conexão WebSocket */
		handleNewConnection(socket, url) {
			console.log("🔗 Nova conexão WebSocket detectada:", url);
			const viewId = this.findOrCreateViewId();
			const connection = this.getOrCreateConnection(viewId);
			connection.websocket = socket;
			this.attachEventListeners(socket, viewId);
		}
		// ===== GERENCIAMENTO DE CONEXÃO =====
		/** Obtém ou cria uma conexão para a visualização especificada */
		getOrCreateConnection(viewId) {
			if (this.connections.has(viewId)) {
				return this.connections.get(viewId);
			}
			const connection = {
				handshake: undefined,
				websocket: undefined,
				latency: undefined,
				lastPingTime: undefined,
				playBlock: undefined,
				rejectionCount: 0,
				retryCount: 3,
			};
			this.connections.set(viewId, connection);
			return connection;
		}
		/** Cria uma nova conexão WebSocket para a visualização especificada */
		createConnection(viewId) {
			try {
				const websocket = new this.originalWebSocket(`ws://${CONFIG.NETWORK.WEBSOCKET_URL}`);
				this.attachEventListeners(websocket, viewId);
				return websocket;
			} catch (error) {
				console.error("❌ Falha ao criar conexão WebSocket:", error);
				this.showConnectionError();
				return undefined;
			}
		}
		// ===== OUVINTES DE EVENTOS =====
		/** Anexa ouvintes de eventos à instância do WebSocket */
		attachEventListeners(socket, viewId) {
			const originalSend = socket.send.bind(socket);
			// Intercepta mensagens enviadas
			socket.send = data => {
				this.logMessage(viewId, data, "sent");
				return originalSend(data);
			};
			// Anexa manipuladores de eventos do WebSocket
			socket.addEventListener("open", () => this.handleConnectionOpen(socket, viewId));
			socket.addEventListener("message", event => this.handleMessage(event, viewId));
			socket.addEventListener("error", event => this.handleConnectionError(event, viewId));
			socket.addEventListener("close", event => this.handleConnectionClose(event, viewId));
		}
		// ===== MANIPULADORES DE EVENTOS =====
		/** Lida com o evento de abertura da conexão WebSocket */
		handleConnectionOpen(socket, viewId) {
			console.log("✅ WebSocket conectado:", socket.url);
			const connection = this.connections.get(viewId);
			if (!connection) {
				socket.close();
				return;
			}
			// Reseta contadores de erro em conexão bem-sucedida
			connection.rejectionCount = 0;
			connection.retryCount = 3;
			// Envia handshake inicial
			socket.send(this.textEncoder.encode(""));
		}
		/** Lida com mensagens recebidas do WebSocket */
		handleMessage(event, viewId) {
			const connection = this.connections.get(viewId);
			if (!connection) return;
			const dataView = new DataView(event.data);
			this.logMessage(viewId, event.data, "received");
			// Processa handshake se ainda não estabelecido
			if (!connection.handshake) {
				this.processHandshake(dataView, connection);
				return;
			}
			// Decodifica o opcode da mensagem e processa
			const rawOpcode = dataView.getUint8(0);
			const decodedOpcode = connection.handshake.decryptTable[rawOpcode];
			dataView.setUint8(0, decodedOpcode);
			this.processGameMessage(dataView, decodedOpcode, viewId, connection);
		}
		/** Lida com erros de conexão WebSocket */
		handleConnectionError(event, viewId) {
			console.error("💥 Erro no WebSocket:", event);
		}
		/** Lida com o fechamento da conexão WebSocket */
		handleConnectionClose(event, viewId) {
			console.log("🔌 WebSocket fechado:", event.code, event.reason);
			const connection = this.connections.get(viewId);
			if (!connection) return;
			this.resetConnectionState(connection);
			this.attemptReconnection(viewId, connection);
		}
		// ===== PROCESSAMENTO DE MENSAGENS =====
		/** Processa a mensagem inicial de handshake para estabelecer tabelas de criptografia */
		processHandshake(dataView, connection) {
			const HANDSHAKE_OFFSET = 10;
			const TABLE_SIZE = 256;
			const encryptTable = new Uint8Array(TABLE_SIZE);
			const decryptTable = new Uint8Array(TABLE_SIZE);
			// Constrói tabelas de lookup de criptografia/descriptografia
			for (let i = 0; i < TABLE_SIZE; i++) {
				const encryptedValue = dataView.getUint8(HANDSHAKE_OFFSET + i);
				encryptTable[i] = encryptedValue;
				decryptTable[encryptedValue] = i;
			}
			connection.handshake = { encryptTable, decryptTable };
			console.log("🤝 Handshake estabelecido com sucesso");
		}
		/** Direciona mensagens do jogo decodificadas com base no opcode */
		processGameMessage(dataView, opcode, viewId, connection) {
			const timestamp = performance.now();
			switch (opcode) {
				case exports.NetworkOpcodeEnum.WorldUpdate:
					this.processWorldUpdate(dataView, viewId, timestamp);
					break;
				case exports.NetworkOpcodeEnum.PositionUpdate:
					this.processPositionUpdate(dataView, viewId);
					break;
				case exports.NetworkOpcodeEnum.Leaderboard:
					this.processLeaderboard(dataView, viewId);
					break;
				case exports.NetworkOpcodeEnum.ClearCells:
					this.processClearCells(dataView, viewId);
					break;
				case exports.NetworkOpcodeEnum.RemoveCells:
					this.processRemoveCells(dataView, viewId);
					break;
				case exports.NetworkOpcodeEnum.MapBorders:
					this.processMapBorders(dataView, viewId);
					break;
				case exports.NetworkOpcodeEnum.OwnCells:
					this.processOwnCells(dataView, viewId, timestamp);
					break;
				case exports.NetworkOpcodeEnum.ChatMessage:
					this.processChatMessage(dataView, viewId);
					break;
				case exports.NetworkOpcodeEnum.ServerStats:
					this.processServerStats(dataView, viewId, connection, timestamp);
					break;
				case exports.NetworkOpcodeEnum.PasswordError:
					this.processPasswordError();
					break;
				default:
					console.log(`❓ Opcode desconhecido: 0x${opcode.toString(16)}`);
			}
		}
		// ===== PROCESSADORES DE MENSAGENS DO JOGO =====
		processWorldUpdate(dataView, viewId, timestamp) {
			console.log("🌍 Processando atualização do mundo...");
			// TODO: Implementar processamento do estado do mundo
		}
		processPositionUpdate(dataView, viewId) {
			const x = dataView.getFloat32(1, true);
			const y = dataView.getFloat32(5, true);
			const scale = dataView.getFloat32(9, true);
			console.log(`📍 Posição da câmera: (${x.toFixed(2)}, ${y.toFixed(2)}) escala: ${scale.toFixed(3)}`);
		}
		processLeaderboard(dataView, viewId) {
			console.log("🏆 Processando atualização do ranking...");
			// TODO: Implementar processamento do ranking
		}
		processClearCells(dataView, viewId) {
			console.log("🧹 Limpando todas as células...");
			// TODO: Implementar limpeza de células
		}
		processRemoveCells(dataView, viewId) {
			console.log("❌ Removendo células específicas...");
			// TODO: Implementar remoção de células específicas
		}
		processMapBorders(dataView, viewId) {
			const left = dataView.getFloat64(1, true);
			const top = dataView.getFloat64(9, true);
			const right = dataView.getFloat64(17, true);
			const bottom = dataView.getFloat64(25, true);
			console.log(`🗺️ Limites do mapa - L:${left} T:${top} R:${right} B:${bottom}`);
		}
		processOwnCells(dataView, viewId, timestamp) {
			const cellId = dataView.getUint32(1, true);
			console.log(`👤 Própria célula adicionada: ${cellId}`);
			// TODO: Implementar rastreamento de células próprias
		}
		processChatMessage(dataView, viewId) {
			console.log("💬 Processando mensagem de chat...");
			// TODO: Implementar parsing de mensagens de chat
		}
		processServerStats(dataView, viewId, connection, timestamp) {
			// Calcula latência se o ping foi enviado
			if (connection.lastPingTime !== undefined) {
				connection.latency = timestamp - connection.lastPingTime;
				console.log(`🏓 Latência atualizada: ${connection.latency.toFixed(1)}ms`);
			}
			connection.lastPingTime = undefined;
		}
		processPasswordError() {
			console.error("🔒 Falha na autenticação da senha!");
			// TODO: Implementar lógica de nova tentativa de senha
		}
		// ===== GERENCIAMENTO DO ESTADO DA CONEXÃO =====
		/** Reseta o estado da conexão após desconexão */
		resetConnectionState(connection) {
			connection.handshake = undefined;
			connection.latency = undefined;
			connection.lastPingTime = undefined;
			connection.playBlock = undefined;
			connection.rejectionCount++;
			connection.websocket = undefined;
			if (connection.retryCount > 0) {
				connection.retryCount--;
			}
		}
		/** Tenta reconectar com estratégia de backoff */
		attemptReconnection(viewId, connection) {
			const baseDelay = 500;
			const longDelay = 5000;
			const maxRejections = 5;
			if (connection.retryCount > 0) {
				setTimeout(() => this.createConnection(viewId), baseDelay);
			} else {
				// Reseta o contador de tentativas e usa delay maior se houver muitas rejeições
				connection.retryCount = 3;
				const delay = connection.rejectionCount >= maxRejections ? longDelay : baseDelay;
				setTimeout(() => this.createConnection(viewId), delay);
			}
		}
		// ===== TAREFAS PERIÓDICAS =====
		/** Inicia tarefas periódicas para monitoramento e atualizações */
		startPeriodicTasks() {
			this.startPingMonitoring();
			this.startConnectionMonitoring();
			this.startQuestTimers();
		}
		/** Envia mensagens periodicas de ping para medir latência */
		startPingMonitoring() {
			setInterval(() => {
				for (const connection of this.connections.values()) {
					if (!this.isConnectionReady(connection)) continue;
					// marca a conexão como não responsiva se o ping anterior não foi respondido
					if (connection.lastPingTime !== undefined) connection.latency = -1;
					connection.lastPingTime = performance.now();
					this.sendPing(connection);
				}
			}, WebSocketInterceptor.PING_INTERVAL);
		}
		/** Monitora as conexões WebSocket para garantir que estão ativas e correspondem ao jogo atual */
		startConnectionMonitoring() {
			setInterval(() => {
				const currentGameUrl = this.getCurrentGameUrl();
				for (const connection of this.connections.values()) {
					if (!connection.websocket) continue;
					if (!this.isConnectionActive(connection.websocket)) continue;
					if (connection.websocket.url === currentGameUrl) continue;
					// Fecha a conexão se a URL não corresponder ao jogo atual
					connection.websocket.close();
				}
			}, WebSocketInterceptor.GAMEMODE_CHECK_INTERVAL);
		}
		/** Envia periodicamente atualizações de tempo da missão */
		startQuestTimers() {
			setInterval(() => {
				for (const viewId of this.connections.keys()) {
					this.sendQuestTime(viewId);
				}
			}, WebSocketInterceptor.QUEST_TIMER_INTERVAL);
		}
		// ===== UTILITY METHODS =====
		/** Verifica se a conexão está pronta para comunicação */
		isConnectionReady(connection) {
			var _a;
			return !!(connection.handshake && ((_a = connection.websocket) === null || _a === void 0 ? void 0 : _a.readyState) === WebSocket.OPEN);
		}
		/** Verifica se a conexão WebSocket está ativa */
		isConnectionActive(websocket) {
			return websocket.readyState === WebSocket.CONNECTING || websocket.readyState === WebSocket.OPEN;
		}
		/** Envia mensagem ping para a conexão */
		sendPing(connection) {
			if (!connection.handshake || !connection.websocket) return;
			const pingOpcode = 0xfe;
			const pingData = connection.handshake.encryptTable.slice(pingOpcode, pingOpcode + 1);
			connection.websocket.send(pingData);
		}
		/** Envia mensagem opcode simples */
		sendOpcode(viewId, opcode) {
			const connection = this.connections.get(viewId);
			if (!this.isConnectionReady(connection)) return;
			const opcodeData = connection.handshake.encryptTable.slice(opcode, opcode + 1);
			connection.websocket.send(opcodeData);
		}
		/** Logs message for debugging and analysis */
		logMessage(viewId, data, direction) {
			this.messageLog.push({
				timestamp: performance.now(),
				viewId,
				data,
				direction,
			});
			// Limite de tamanho do buffer de mensagens
			if (this.messageLog.length > WebSocketInterceptor.MESSAGE_BUFFER_LIMIT) {
				this.messageLog.splice(0, this.messageLog.length - WebSocketInterceptor.MESSAGE_BUFFER_TRIM_SIZE);
			}
		}
		/** Busca ou cria um identificador de visualização */
		findOrCreateViewId() {
			// TODO: Implementar gerenciamento de visualizações baseado em abas do jogo
			return Symbol("primary-view");
		}
		/** Retorna a URL do servidor de jogo atual */
		getCurrentGameUrl() {
			return `ws://${CONFIG.NETWORK.WEBSOCKET_URL}`;
		}
		/** Exibe mensagem de erro de conexão */
		showConnectionError() {
			console.error("❌ Connection failed: Check server address or security settings");
		}
		// ===== PUBLIC API =====
		/** Envia atualização de tempo da missão para a visualização especificada */
		sendQuestTime(viewId) {
			const questTimeOpcode = 0xbf;
			this.sendOpcode(viewId, questTimeOpcode);
		}
		/** Obtém informações sobre todas as conexões ativas */
		getAllConnectionsInfo() {
			return Array.from(this.connections.entries()).map(([viewId, connection]) => {
				var _a, _b;
				return {
					viewId,
					url: (_a = connection.websocket) === null || _a === void 0 ? void 0 : _a.url,
					readyState: (_b = connection.websocket) === null || _b === void 0 ? void 0 : _b.readyState,
					latency: connection.latency,
					rejectionCount: connection.rejectionCount,
					retryCount: connection.retryCount,
				};
			});
		}
		/** Obtém as mensagens recentes do log */
		getRecentMessages(maxAgeSeconds = 10) {
			const cutoffTime = performance.now() - maxAgeSeconds * 1000;
			return this.messageLog.filter(entry => entry.timestamp > cutoffTime);
		}
		/** Obtém informações de estatísticas de rede */
		getNetworkStats() {
			const connections = Array.from(this.connections.values());
			const activeConnections = connections.filter(c => {
				var _a;
				return ((_a = c.websocket) === null || _a === void 0 ? void 0 : _a.readyState) === WebSocket.OPEN;
			});
			const validLatencies = connections.map(c => c.latency).filter(lat => lat !== undefined && lat > 0);
			return {
				totalConnections: this.connections.size,
				activeConnections: activeConnections.length,
				totalMessages: this.messageLog.length,
				averageLatency: validLatencies.length > 0 ? validLatencies.reduce((sum, lat) => sum + lat, 0) / validLatencies.length : 0,
			};
		}
	}
	// Constantes de configuração
	WebSocketInterceptor.PING_INTERVAL = 2000;
	WebSocketInterceptor.GAMEMODE_CHECK_INTERVAL = 200;
	WebSocketInterceptor.QUEST_TIMER_INTERVAL = 1000;
	WebSocketInterceptor.MESSAGE_BUFFER_LIMIT = 1000;
	WebSocketInterceptor.MESSAGE_BUFFER_TRIM_SIZE = 500;
	class World {
		constructor() {
			// Armazenamento principal de entidades do jogo
			this.cells = new Map();
			this.pellets = new Map();
			// Símbolos únicos para identificar diferentes abas/visualizações
			this.multis = Array.from({ length: 8 }, () => Symbol());
			// IDs das visualizações principais
			this.viewId = {
				primary: this.multis[0],
				secondary: this.multis[1],
				spectate: Symbol(),
			};
			// Visualização atualmente selecionada
			this.selected = this.viewId.primary;
			// Mapa de todas as visualizações ativas
			this.views = new Map();
			// Estado de sincronização entre abas
			this.synchronized = false;
			this.wasFlawlessSynchronized = false;
			this.lastClean = performance.now();
			// Estatísticas do jogador
			this.stats = {
				foodEaten: 0,
				highestPosition: 200,
				highestScore: 0,
				spawnedAt: undefined,
			};
			this.settings = CONFIG.DEFAULT_SETTINGS;
		}
		// Padrão Singleton para garantir uma única instância
		static getInstance() {
			if (!World.instance) {
				World.instance = new World();
			}
			return World.instance;
		}
		// ===== MÉTODOS PÚBLICOS =====
		// Verifica se há células vivas em qualquer visualização
		isAlive() {
			var _a;
			for (const [view, vision] of this.views) {
				for (const id of vision.owned) {
					const cell = this.cells.get(id);
					// Se a célula não existe ainda, consideramos como viva
					if (!cell) return true;
					const frame = (_a = cell.views.get(view)) === null || _a === void 0 ? void 0 : _a.frames[0];
					if ((frame === null || frame === void 0 ? void 0 : frame.deadAt) === undefined) return true;
				}
			}
			return false;
		}
		// Calcula a câmera para uma visualização específica
		calculateSingleCamera(view, vision, weightExponent = 0, now = performance.now()) {
			var _a, _b;
			vision = vision !== null && vision !== void 0 ? vision : this.views.get(view);
			if (!vision) {
				return { mass: 0, scale: 1, sumX: 0, sumY: 0, weight: 0 };
			}
			let mass = 0;
			let r = 0;
			let sumX = 0;
			let sumY = 0;
			let weight = 0;
			// Processa todas as células possuídas por esta visualização
			for (const id of vision.owned) {
				const cell = this.cells.get(id);
				const frame = this.synchronized
					? cell === null || cell === void 0
						? void 0
						: cell.merged
					: (_a = cell === null || cell === void 0 ? void 0 : cell.views.get(view)) === null || _a === void 0
					? void 0
					: _a.frames[0];
				const interp = this.synchronized
					? cell === null || cell === void 0
						? void 0
						: cell.merged
					: (_b = cell === null || cell === void 0 ? void 0 : cell.views.get(view)) === null || _b === void 0
					? void 0
					: _b.frames[0];
				if (!frame || !interp) continue;
				// Não incluir células possuídas antes do respawn
				if (frame.born < vision.spawned) continue;
				if (this.settings.cameraMovement === "instant") {
					const xyr = this.calculatePosition(frame, interp, undefined, undefined, false, now);
					r += xyr.r * xyr.a;
					mass += ((xyr.r * xyr.r) / 100) * xyr.a;
					const cellWeight = xyr.a * Math.pow(xyr.r, weightExponent);
					sumX += xyr.x * cellWeight;
					sumY += xyr.y * cellWeight;
					weight += cellWeight;
				} else {
					// Movimento de câmera padrão
					if (frame.deadAt !== undefined) continue;
					const xyr = this.calculatePosition(frame, interp, undefined, undefined, false, now);
					r += frame.nr;
					mass += (frame.nr * frame.nr) / 100;
					const cellWeight = Math.pow(frame.nr, weightExponent);
					sumX += xyr.x * cellWeight;
					sumY += xyr.y * cellWeight;
					weight += cellWeight;
				}
			}
			const scale = Math.pow(Math.min(64 / r, 1), 0.4);
			return { mass, scale, sumX, sumY, weight };
		}
		// Calcula e atualiza as posições de câmera para todas as visualizações
		updateCameras(now = performance.now()) {
			const weightExponent = this.settings.camera !== "default" ? 2 : 0;
			// Cria conjuntos disjuntos de todas as câmeras próximas
			const cameras = new Map();
			const sets = new Map();
			for (const [view, vision] of this.views) {
				cameras.set(view, this.calculateSingleCamera(view, vision, weightExponent, now));
				sets.set(view, new Set([view]));
			}
			// Calcula mesmo se as abas não forem realmente mescladas (para contornos multi)
			if (this.settings.multibox || this.settings.nbox) {
				this.mergeCameraSets(cameras, sets, now);
			}
			// Calcula e atualiza posições de câmera mescladas
			this.updateMergedCameras(cameras, sets, now);
		}
		// Cria ou retorna uma visualização existente
		createVision(view) {
			const existing = this.views.get(view);
			if (existing) return existing;
			const vision = {
				border: undefined,
				camera: {
					x: 0,
					tx: 0,
					y: 0,
					ty: 0,
					scale: 0,
					tscale: 0,
					merged: false,
					updated: performance.now() - 1,
				},
				leaderboard: [],
				owned: new Set(),
				spawned: -Infinity,
				stats: undefined,
				used: -Infinity,
			};
			this.views.set(view, vision);
			return vision;
		}
		// Sincroniza frames entre diferentes visualizações
		synchronizeViews() {
			if (this.wasFlawlessSynchronized && this.settings.synchronization !== "flawless") this.cleanupFrameHistory();
			if (!this.settings.synchronization || this.views.size <= 1) {
				this.resetSynchronization();
				return;
			}
			const now = performance.now();
			const indices = {};
			if (this.settings.synchronization === "flawless") {
				this.performFlawlessSynchronization(indices, now);
			} else {
				this.performLatestSynchronization(indices);
			}
			this.mergeFrames(indices, now);
			this.cleanupFrameHistory();
			this.updateSynchronizationState(now);
		}
		calculateScore(view) {
			var _a;
			let score = 0;
			const vision = this.views.get(view);
			if (!vision) return 0;
			for (const id of vision.owned) {
				const cell = this.cells.get(id);
				if (!cell) continue;
				const frame = this.synchronized ? cell.merged : (_a = cell.views.get(view)) === null || _a === void 0 ? void 0 : _a.frames[0];
				if (!frame || frame.deadAt !== undefined) continue;
				// Usa pontuação exata do servidor, sem interpolação
				score += (frame.nr * frame.nr) / 100;
			}
			return score;
		}
		// Calcula posição interpolada de uma célula
		calculatePosition(frame, interp, killerFrame, killerInterp, isPellet = false, now = performance.now()) {
			let nx = frame.nx;
			let ny = frame.ny;
			// Anima em direção à posição interpolada do assassino para suavidade extra
			if (killerFrame && killerInterp) {
				const killerXyr = this.calculatePosition(killerFrame, killerInterp, undefined, undefined, false, now);
				nx = killerXyr.x;
				ny = killerXyr.y;
			}
			let x, y, r, a;
			if (isPellet && frame.deadAt === undefined) {
				// Pellets não se movem suavemente
				x = nx;
				y = ny;
				r = frame.nr;
				a = 1;
			} else {
				// Interpolação suave para células
				let alpha = (now - interp.updated) / this.settings.drawDelay;
				alpha = Math.max(0, Math.min(1, alpha));
				x = interp.ox + (nx - interp.ox) * alpha;
				y = interp.oy + (ny - interp.oy) * alpha;
				r = interp.or + (frame.nr - interp.or) * alpha;
				const targetA = frame.deadAt !== undefined ? 0 : 1;
				a = interp.a + (targetA - interp.a) * alpha;
			}
			const dt = (now - interp.updated) / 1000;
			const jellyPhysicsSpeed = this.settings.slowerJellyPhysics ? 10 : 5;
			return {
				x,
				y,
				r,
				jr: this.exponentialEase(interp.jr, r, jellyPhysicsSpeed, dt),
				a,
			};
		}
		// Remove células mortas e invisíveis
		cleanupDeadCells() {
			const now = performance.now();
			if (now - this.lastClean < 200) return;
			this.lastClean = now;
			for (const collection of [this.cells, this.pellets]) {
				for (const [id, cell] of collection) {
					for (const [view, record] of cell.views) {
						const firstFrame = record.frames[0];
						const lastFrame = record.frames[record.frames.length - 1];
						if (firstFrame.deadAt !== lastFrame.deadAt) continue;
						if (lastFrame.deadAt !== undefined && now - lastFrame.deadAt >= this.settings.drawDelay + 200) {
							cell.views.delete(view);
						}
					}
					if (cell.views.size === 0) {
						collection.delete(id);
					}
				}
			}
		}
		// ===== MÉTODOS PRIVADOS =====
		// Mescla conjuntos de câmeras próximas
		mergeCameraSets(cameras, sets, now) {
			for (const [view, vision] of this.views) {
				const set = sets.get(view);
				const camera = cameras.get(view);
				if (camera.weight <= 0 || now - vision.used > 20000) continue; // Não mesclar abas inativas
				const x = camera.sumX / camera.weight;
				const y = camera.sumY / camera.weight;
				const width = 1920 / 2 / camera.scale;
				const height = 1080 / 2 / camera.scale;
				for (const [otherView, otherVision] of this.views) {
					const otherSet = sets.get(otherView);
					if (set === otherSet || now - otherVision.used > 20000) continue;
					const otherCamera = cameras.get(otherView);
					if (otherCamera.weight <= 0) continue;
					const otherX = otherCamera.sumX / otherCamera.weight;
					const otherY = otherCamera.sumY / otherCamera.weight;
					const otherWidth = 1920 / 2 / otherCamera.scale;
					const otherHeight = 1080 / 2 / otherCamera.scale;
					// Limite de proximidade baseado na massa de ambas as abas
					const threshold = 1000 + Math.min(camera.weight / 100 / 25, otherCamera.weight / 100 / 25);
					if (Math.abs(x - otherX) <= width + otherWidth + threshold && Math.abs(y - otherY) <= height + otherHeight + threshold) {
						// Mesclar conjuntos disjuntos
						for (const connectedView of otherSet) {
							set.add(connectedView);
							sets.set(connectedView, set);
						}
					}
				}
			}
		}
		// Atualiza câmeras mescladas
		updateMergedCameras(cameras, sets, now) {
			const computed = new Set();
			for (const set of sets.values()) {
				if (computed.has(set)) continue;
				let mass = 0;
				let sumX = 0;
				let sumY = 0;
				let weight = 0;
				if (this.settings.mergeCamera) {
					for (const view of set) {
						const camera = cameras.get(view);
						mass += camera.mass;
						sumX += camera.sumX;
						sumY += camera.sumY;
						weight += camera.weight;
					}
				}
				for (const view of set) {
					const vision = this.views.get(view);
					if (!this.settings.mergeCamera) {
						const camera = cameras.get(view);
						({ mass, sumX, sumY, weight } = camera);
					}
					this.updateSingleCameraPosition(vision, mass, sumX, sumY, weight, set.size > 1, now);
				}
				computed.add(set);
			}
		}
		// Atualiza posição de uma única câmera
		updateSingleCameraPosition(vision, mass, sumX, sumY, weight, isMerged, now) {
			let xyFactor;
			if (weight <= 0) {
				xyFactor = 20;
			} else if (this.settings.cameraMovement === "instant") {
				xyFactor = 1;
			} else {
				// Movimento de câmera suave após spawnar
				const aliveFor = (performance.now() - vision.spawned) / 1000;
				const a = Math.max(0, Math.min(1, (aliveFor - 0.3) / 0.3));
				const base = this.settings.cameraSpawnAnimation ? 2 : 1;
				xyFactor = Math.min(this.settings.cameraSmoothness, base * (1 - a) + this.settings.cameraSmoothness * a);
			}
			if (weight > 0) {
				vision.camera.tx = sumX / weight;
				vision.camera.ty = sumY / weight;
				let scale;
				if (this.settings.camera === "default") {
					scale = Math.pow(Math.min(64 / Math.sqrt(mass), 1), 0.4);
				} else {
					scale = Math.pow(Math.min(64 / Math.sqrt(100 * mass), 1), 0.4);
				}
				vision.camera.tscale = this.settings.autoZoom ? scale : 0.25;
			}
			const dt = (now - vision.camera.updated) / 1000;
			vision.camera.x = this.exponentialEase(vision.camera.x, vision.camera.tx, xyFactor, dt);
			vision.camera.y = this.exponentialEase(vision.camera.y, vision.camera.ty, xyFactor, dt);
			vision.camera.scale = this.exponentialEase(vision.camera.scale, vision.camera.tscale, 9, dt);
			vision.camera.merged = isMerged;
			vision.camera.updated = now;
		}
		// Implementa sincronização perfeita entre visualizações (versão simplificada)
		performFlawlessSynchronization(indices, now) {
			// Versão simplificada da sincronização perfeita
			// Em uma implementação real, isso envolveria algoritmos complexos de grafos bipartidos
			let i = 0;
			for (const view of this.views.keys()) {
				indices[i++] = indices[view] = 0; // Por simplicidade, usar sempre índice 0
			}
			this.wasFlawlessSynchronized = true;
		}
		// Implementa sincronização usando frames mais recentes
		performLatestSynchronization(indices) {
			let i = 0;
			for (const view of this.views.keys()) {
				indices[i++] = indices[view] = 0;
			}
			this.wasFlawlessSynchronized = false;
		}
		// Mescla frames baseado nos índices encontrados
		mergeFrames(indices, now) {
			for (const collection of [this.cells, this.pellets]) {
				for (const cell of collection.values()) {
					// Encontra frame modelo (versão simplificada)
					let modelFrame;
					for (const [view, record] of cell.views) {
						const frame = record.frames[indices[view] || 0];
						if (frame && !modelFrame) {
							modelFrame = frame;
						}
					}
					if (modelFrame) {
						cell.model = modelFrame;
					}
				}
			}
			// Atualiza frames mesclados
			this.updateMergedFrames(now);
		}
		// Atualiza frames mesclados para todas as células
		updateMergedFrames(now) {
			var _a;
			for (const collection of [this.cells, this.pellets]) {
				for (const cell of collection.values()) {
					const { model, merged } = cell;
					if (!model) {
						cell.merged = undefined;
						continue;
					}
					if (!merged || (merged.deadAt !== undefined && model.deadAt === undefined)) {
						// Cria novo frame mesclado
						cell.merged = {
							nx: model.nx,
							ny: model.ny,
							nr: model.nr,
							born: now,
							deadAt: model.deadAt !== undefined ? now : undefined,
							deadTo: model.deadTo,
							ox: model.nx,
							oy: model.ny,
							or: model.nr,
							jr: model.nr,
							a: 0,
							updated: now,
						};
					} else {
						// Atualiza frame mesclado existente
						if (
							merged.deadAt === undefined &&
							(model.deadAt !== undefined || model.nx !== merged.nx || model.ny !== merged.ny || model.nr !== merged.nr)
						) {
							const isPellet = collection === this.pellets;
							const xyr = this.calculatePosition(merged, merged, undefined, undefined, isPellet, now);
							merged.ox = xyr.x;
							merged.oy = xyr.y;
							merged.or = xyr.r;
							merged.jr = xyr.jr;
							merged.a = xyr.a;
							merged.updated = now;
						}
						merged.nx = model.nx;
						merged.ny = model.ny;
						merged.nr = model.nr;
						merged.deadAt = model.deadAt !== undefined ? ((_a = merged.deadAt) !== null && _a !== void 0 ? _a : now) : undefined;
						merged.deadTo = model.deadTo;
					}
				}
			}
		}
		// Remove histórico de frames
		cleanupFrameHistory() {
			for (const collection of [this.cells, this.pellets]) {
				for (const cell of collection.values()) {
					for (const record of cell.views.values()) {
						// Remove frames antigos, mantendo apenas o atual
						while (record.frames.length > 1) {
							record.frames.pop();
						}
					}
				}
			}
		}
		// Reset do estado de sincronização
		resetSynchronization() {
			this.disagreementStart = undefined;
			this.disagreementAt = undefined;
			this.synchronized = false;
			this.wasFlawlessSynchronized = false;
		}
		// Atualiza estado de sincronização
		updateSynchronizationState(now) {
			this.disagreementStart = undefined;
			// Se houve desacordo, espera um tempo antes de reativar sincronização
			if (this.disagreementAt === undefined || now - this.disagreementAt > 1000) {
				this.synchronized = true;
			}
		}
		// Utilitário para easing exponencial
		exponentialEase(current, target, factor, deltaTime) {
			return current + (target - current) * (1 - Math.exp(-factor * deltaTime));
		}
		// ===== GETTERS PÚBLICOS =====
		get gameStats() {
			return Object.assign({}, this.stats);
		}
		get currentView() {
			return this.selected;
		}
		get allViews() {
			return this.views;
		}
		get isSynchronized() {
			return this.synchronized;
		}
		get cellCount() {
			return this.cells.size;
		}
		get pelletCount() {
			return this.pellets.size;
		}
	}
	class UIManager {
		constructor() {
			this.isInitialized = false;
			this.keyBindManager = KeyBindManager.getInstance();
		}
		static getInstance() {
			if (!UIManager.instance) UIManager.instance = new UIManager();
			return UIManager.instance;
		}
		initialize() {
			if (this.isInitialized) return true;
			this.keyBindManager.register({
				key: "F1",
				handler: () => alert("Ajuda"),
				description: "Mostra a ajuda",
			});
			this.keyBindManager.register({
				key: "F2",
				handler: () => {
					const interceptor = WebSocketInterceptor.getInstance();
					const info = interceptor.getAllConnectionsInfo();
					console.log("🔍 Informações do WebSocket:", info);
					const recent = interceptor.getRecentMessages(30);
					console.log("🕒 Mensagens dos últimos 30 segundos:", recent);
				},
				description: "Mostra informações do WebSocket",
			});
			this.isInitialized = true;
			return true;
		}
		destroy() {
			this.isInitialized = false;
		}
	}
	// ===== APLICAÇÃO PRINCIPAL ESSENCIAL =====
	class ScriptApplication {
		constructor() {
			this.isInitialized = false;
			this.settingsStore = SettingsStore.getInstance();
			this.uiManager = UIManager.getInstance();
			this.wsInterceptor = WebSocketInterceptor.getInstance();
			this.world = World.getInstance();
		}
		static getInstance() {
			if (!ScriptApplication.instance) ScriptApplication.instance = new ScriptApplication();
			return ScriptApplication.instance;
		}
		async initialize() {
			if (this.isInitialized) return true;
			if (document.readyState === "loading") await new Promise(resolve => document.addEventListener("DOMContentLoaded", resolve));
			this.uiManager.initialize();
			this.settingsStore.setSetting("appVersion", CONFIG.VERSION);
			console.log("🚀 Script inicializado com interceptação WebSocket");
			console.log("ℹ️  Pressione F2 para ver status do WebSocket");
			this.isInitialized = true;
			return true;
		}
	}
	// ===== INICIALIZAÇÃO AUTOMÁTICA =====
	ScriptApplication.getInstance().initialize();

	return exports;
})({});
