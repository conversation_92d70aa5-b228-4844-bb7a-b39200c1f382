Vou explicar detalhadamente a arquitetura fundamental do sistema multiboxing do Sigmally Fixes V2:

## 1. **Estrutura do Objeto `world`**

### Inicialização e Propriedades Principais:

```javascript path=fixes.js mode=EXCERPT
const world = (() => {
    const world = {};

    // #1 : define cell variables and functions
    /** @type {Map<number, Cell>} */
    world.cells = new Map();
    /** @type {Map<number, Cell>} */
    world.pellets = new Map();
    world.multis = [Symbol(), Symbol(), Symbol(), Symbol(), Symbol(), Symbol(), Symbol(), Symbol()];
    world.viewId = {
        primary: world.multis[0],
        secondary: world.multis[1],
        spectate: Symbol(),
    };
    world.selected = world.viewId.primary;
    /** @type {Map<symbol, Vision>} */
    world.views = new Map();
```

### Estruturas de Dados Fundamentais:

```javascript path=fixes.js mode=EXCERPT
/**
 * @typedef {{
 * 	nx: number, ny: number, nr: number,
 * 	born: number, deadAt: number | undefined, deadTo: number,
 * }} CellFrameWritable
 * @typedef {Readonly<CellFrameWritable>} CellFrame
 * @typedef {{
 * 	ox: number, oy: number, or: number,
 * 	jr: number, a: number, updated: number,
 * }} CellInterpolation
 * @typedef {{
 *  name: string, skin: string, sub: boolean, clan: string,
 * 	rgb: [number, number, number],
 * 	jagged: boolean, eject: boolean,
 * }} CellDescription
 * @typedef {CellInterpolation & CellDescription & { frames: CellFrame[] }} CellRecord
 * @typedef {{
 * 	id: number,
 * 	merged: (CellFrameWritable & CellInterpolation) | undefined,
 * 	model: CellFrame | undefined,
 * 	views: Map<symbol, CellRecord>,
 * }} Cell
```

### Estrutura do Vision Object:

```javascript path=fixes.js mode=EXCERPT
/**
 * @typedef {{
 * 	border: { l: number, r: number, t: number, b: number } | undefined,
 * 	camera: {
 * 		x: number, tx: number,
 * 		y: number, ty: number,
 * 		scale: number, tscale: number,
 * 		merged: boolean,
 * 		updated: number,
 * 	},
 * 	leaderboard: { name: string, me: boolean, sub: boolean, place: number | undefined }[],
 * 	owned: Set<number>,
 * 	spawned: number,
 * 	stats: object | undefined,
 * 	used: number,
 * }} Vision
 */
```

## 2. **Fluxo de Dados das Conexões WebSocket**

### Interceptação e Processamento de Mensagens:

```javascript path=fixes.js mode=EXCERPT
ws.addEventListener("message", e => {
	const connection = net.connections.get(view);
	const vision = world.views.get(view);
	if (!connection || !vision) return ws.close();
	const dat = new DataView(e.data);

	if (!connection.handshake) {
		// skip version "SIG 0.0.1\0"
		let o = 10;

		const shuffle = new Uint8Array(256);
		const unshuffle = new Uint8Array(256);
		for (let i = 0; i < 256; ++i) {
			const shuffled = dat.getUint8(o + i);
			shuffle[i] = shuffled;
			unshuffle[shuffled] = i;
		}

		connection.handshake = { shuffle, unshuffle };
		return;
	}

	// do this so the packet can easily be sent to sigmod afterwards
	dat.setUint8(0, connection.handshake.unshuffle[dat.getUint8(0)]);

	const now = performance.now();
	let o = 1;
	switch (dat.getUint8(0)) {
		case 0x10: {
			// world update
			// ...processamento detalhado
		}
	}
});
```

## 3. **Sistema de Vision e CellRecord**

### Criação e Gerenciamento de Vision:

```javascript path=fixes.js mode=EXCERPT
/** @param {symbol} view */
world.create = view => {
	const old = world.views.get(view);
	if (old) return old;

	const vision = {
		border: undefined,
		camera: { x: 0, tx: 0, y: 0, ty: 0, scale: 0, tscale: 0, merged: false, updated: performance.now() - 1 },
		leaderboard: [],
		owned: new Set(),
		spawned: -Infinity,
		stats: undefined,
		used: -Infinity,
	};
	world.views.set(view, vision);
	return vision;
};
```

### Gerenciamento de Frames de Células:

```javascript path=fixes.js mode=EXCERPT
// carry forward record frames
if (settings.synchronization === "flawless") {
	for (const key of /** @type {const} */ (["cells", "pellets"])) {
		for (const cell of world[key].values()) {
			const record = cell.views.get(view);
			if (!record) continue;

			record.frames.unshift(record.frames[0]);
			for (let i = 12, l = record.frames.length; i < l; ++i) record.frames.pop();
		}
	}
}
```

## 4. **Processamento de Mensagens do Protocolo**

### Decodificação de World Update (0x10):

```javascript path=fixes.js mode=EXCERPT
case 0x10: {
    // world update
    // (a) : read and update cells
    do {
        const id = dat.getUint32(o, true);
        o += 4;
        if (id === 0) break;

        const x = dat.getInt32(o, true);
        const y = dat.getInt32(o + 4, true);
        const r = dat.getUint16(o + 8, true);
        const flags = dat.getUint8(o + 10);
        o += 11;

        const jagged = !!(flags & 0x11); // spiked or agitated
        const eject = !!(flags & 0x20);
        const pellet = r <= 40 && !eject; // tourney servers have bigger pellets (r=40)
        const cell = (pellet ? world.pellets : world.cells).get(id);
        const record = cell?.views.get(view);

        if (record) {
            const frame = record.frames[0];
            if (frame.deadAt === undefined) {
                // update interpolation using old targets
                const xyr = world.xyr(record.frames[0], record, undefined, undefined, pellet, now);
                record.ox = xyr.x;
                record.oy = xyr.y;
                record.or = xyr.r;
                record.jr = xyr.jr;
                record.a = xyr.a;
            }
        }
    } while (true);
}
```

### Parsing de Dados de Células:

```javascript path=fixes.js mode=EXCERPT
// Criação/atualização de CellRecord
const record = {
	ox: x,
	oy: y,
	or: r,
	jr: r,
	a: 0,
	updated: now,
	name,
	skin,
	sub,
	clan,
	rgb,
	jagged,
	eject,
	frames: [
		{
			nx: x,
			ny: y,
			nr: r,
			born: now,
			deadAt: undefined,
			deadTo: 0,
		},
	],
};

if (cell) {
	cell.views.set(view, record);
} else {
	(pellet ? world.pellets : world.cells).set(id, {
		id,
		merged: undefined,
		model: undefined,
		views: new Map([[view, record]]),
	});
}
```

## 5. **Ciclo de Atualização e Sincronização**

### Execução de world.merge():

```javascript path=fixes.js mode=EXCERPT
world.merge = () => {
	if (!settings.synchronization || world.views.size <= 1) {
		disagreementStart = disagreementAt = undefined;
		world.synchronized = false;
		wasFlawlessSynchronized = false;
		return;
	}

	// #4 : find a model frame for all cells and pellets
	for (const key of /** @type {const} */ (["cells", "pellets"])) {
		for (const cell of world[key].values()) {
			/** @type {[symbol, CellRecord] | undefined} */
			let modelPair;
			modelViewLoop: for (const pair of cell.views) {
				if (!modelPair) {
					modelPair = pair;
					continue;
				}
			}

			// in very rare circumstances, this ends up being undefined, for some reason
			if (modelPair) cell.model = modelPair[1].frames[indices[modelPair[0]]] ?? cell.model;
		}
	}

	// #5 : create or update the merged frame for all cells and pellets
	for (const key of /** @type {const} */ (["cells", "pellets"])) {
		for (const cell of world[key].values()) {
			const { model, merged } = cell;
			if (!model) {
				// could happen
				cell.merged = undefined;
				continue;
			}
		}
	}
};
```

### Fluxo Temporal Completo:

```javascript path=fixes.js mode=EXCERPT
// (d) : finalize, upload data
world.merge();
world.clean();
render.upload(true);

// (e) : clear own cells that don't exist anymore (NOT on world.clean!)
for (const id of vision.owned) {
	const cell = world.cells.get(id);
	if (!cell) {
		vision.owned.delete(id);
		continue;
	}
	const record = cell?.views.get(view);

	if (record && record.frames[0].deadAt === undefined && connection.playBlock?.state === "joining") {
		connection.playBlock = undefined;
	}
}

ui.deathScreen.check();
```

### Interpolação e Renderização:

```javascript path=fixes.js mode=EXCERPT
/**
 * @param {CellFrame} frame
 * @param {CellInterpolation} interp
 * @param {CellFrame | undefined} killerFrame
 * @param {CellInterpolation | undefined} killerInterp
 * @param {boolean} pellet
 * @param {number} now
 * @returns {{ x: number, y: number, r: number, jr: number, a: number }}
 */
world.xyr = (frame, interp, killerFrame, killerInterp, pellet, now) => {
	let nx = frame.nx;
	let ny = frame.ny;
	if (killerFrame && killerInterp) {
		// animate towards the killer's interpolated position (not the target position) for extra smoothness
		// we also assume the killer has not died (if it has, then weird stuff is OK to occur)
		const killerXyr = world.xyr(killerFrame, killerInterp, undefined, undefined, false, now);
		nx = killerXyr.x;
		ny = killerXyr.y;
	}
	// ... cálculos de interpolação
};
```

## **Resumo da Arquitetura:**

1. **WebSocket → DataView**: Mensagens brutas são convertidas em estruturas binárias
2. **DataView → CellRecord**: Dados são parseados e organizados por view/conexão
3. **CellRecord → Cell.views**: Múltiplas views mantêm dados da mesma célula
4. **world.merge()**: Combina dados de múltiplas views em objetos `merged`
5. **Interpolação**: `world.xyr()` calcula posições suaves para renderização
6. **Renderização**: Sistema usa dados `synchronized ? merged : view-specific`

O sistema é fundamentalmente **event-driven**, onde cada mensagem WebSocket atualiza estruturas específicas da view, e periodicamente `world.merge()` sincroniza tudo para criar uma visão unificada do mundo do jogo.
