import typescript from '@rollup/plugin-typescript';

export default {
  input: 'index.ts',
  output: {
    file: 'dist/Script-Teste.user.js',
    format: 'iife',
    name: 'ScriptTeste',
    banner: `// ==UserScript==\n// @name         Script Teste\n// @namespace    script-test\n// @version      1.0.0\n// <AUTHOR> @description  Um script de teste para demonstrar funcionalidades avançadas do Tampermonkey\n// @license MIT\n// @match        https://agariobr.com.br/*\n// @grant        none\n// @run-at       document-start\n// ==/UserScript==` 
  },
  plugins: [typescript()],
};
