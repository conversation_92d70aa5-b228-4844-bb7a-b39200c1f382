const net = (() => {
	const net = {};

	// #1 : define state
	/** @type {Map<symbol, {
	 * 		handshake: { shuffle: Uint8Array, unshuffle: Uint8Array } | undefined,
	 * 		latency: number | undefined,
	 * 		pinged: number | undefined,
	 * 		playBlock: { state: 'leaving' | 'joining', started: number } | undefined,
	 * 		rejections: number,
	 * 		retries: number,
	 * 		ws: WebSocket | undefined,
	 * }>} */
	net.connections = new Map();

	/** @param {symbol} view */
	net.create = view => {
		if (net.connections.has(view)) return;

		net.connections.set(view, {
			handshake: undefined,
			latency: undefined,
			pinged: undefined,
			playBlock: undefined,
			rejections: 0,
			retries: 0,
			ws: connect(view),
		});
	};

	let captchaPostQueue = Promise.resolve();

	/**
	 * @param {symbol} view
	 * @param {(() => void)=} establishedCallback
	 * @returns {WebSocket | undefined}
	 */
	const connect = (view, establishedCallback) => {
		if (net.connections.get(view)?.ws) return; // already being handled by another process

		// do not allow sigmod's args[0].includes('sigmally.com') check to pass
		const realUrl = net.url();
		const fakeUrl = /** @type {any} */ ({ includes: () => false, toString: () => realUrl });
		let ws;
		try {
			ws = new WebSocket(fakeUrl);
		} catch (err) {
			console.error("can't make WebSocket:", err);
			aux.require(
				null,
				"The server address is invalid. It probably has a typo.\n" +
					'- If using an insecure address (starting with "ws://" and not "wss://") that isn\'t localhost, ' +
					"enable Insecure Content in this site's browser settings.\n" +
					"- If using a local server, make sure to use localhost and not any other local IP."
			);
			return; // ts-check is dumb
		}

		{
			const con = net.connections.get(view);
			if (con) con.ws = ws;
		}

		ws.binaryType = "arraybuffer";
		ws.addEventListener("close", e => {
			console.error("WebSocket closed:", e);
			establishedCallback?.();
			establishedCallback = undefined;

			const connection = net.connections.get(view);
			const vision = world.views.get(view);
			if (!connection || !vision) return; // if the entry no longer exists, don't reconnect

			connection.handshake = undefined;
			connection.latency = undefined;
			connection.pinged = undefined;
			connection.playBlock = undefined;
			++connection.rejections;
			if (connection.retries > 0) --connection.retries;

			vision.border = undefined;
			// don't reset vision.camera
			vision.owned = new Set();
			vision.leaderboard = [];
			vision.spawned = -Infinity;
			vision.stats = undefined;

			for (const key of /** @type {const} */ (["cells", "pellets"])) {
				for (const [id, resolution] of world[key]) {
					resolution.views.delete(view);
					if (resolution.views.size === 0) world[key].delete(id);
				}
			}

			connection.ws = undefined;
			world.merge();
			render.upload(true);

			const thisUrl = net.url();
			const url = new URL(thisUrl); // use the current url, not realUrl
			const captchaEndpoint = `http${url.protocol === "ws:" ? "" : "s"}://${url.host}/server/recaptcha/v3`;

			/** @param {string} type */
			const requestCaptcha = type => {
				ui.captcha.request(view, type, token => {
					captchaPostQueue = captchaPostQueue.then(
						() =>
							new Promise(resolve => {
								aux.oldFetch(captchaEndpoint, {
									method: "POST",
									headers: { "content-type": "application/json" },
									body: JSON.stringify({ token }),
								})
									.then(res => res.json())
									.then(res => res.status)
									.catch(() => "rejected")
									.then(status => {
										if (status === "complete") connect(view, resolve);
										else setTimeout(() => connect(view, resolve), 1000);
									});
							})
					);
				});
			};

			if (connection.retries > 0) {
				setTimeout(() => connect(view), 500);
			} else {
				aux.oldFetch(captchaEndpoint)
					.then(res => res.json())
					.then(res => res.version)
					.catch(() => "none")
					.then(type => {
						connection.retries = 3;
						if (type === "v2" || type === "v3" || type === "turnstile") requestCaptcha(type);
						else setTimeout(() => connect(view), connection.rejections >= 5 ? 5000 : 500);
					});
			}
		});
		ws.addEventListener("error", () => {});
		ws.addEventListener("message", e => {
			const connection = net.connections.get(view);
			const vision = world.views.get(view);
			if (!connection || !vision) return ws.close();
			const dat = new DataView(e.data);

			if (!connection.handshake) {
				// skip version "SIG 0.0.1\0"
				let o = 10;

				const shuffle = new Uint8Array(256);
				const unshuffle = new Uint8Array(256);
				for (let i = 0; i < 256; ++i) {
					const shuffled = dat.getUint8(o + i);
					shuffle[i] = shuffled;
					unshuffle[shuffled] = i;
				}

				connection.handshake = { shuffle, unshuffle };

				if (world.alive()) net.play(world.selected, input.playData(input.name(view), false));
				return;
			}

			// do this so the packet can easily be sent to sigmod afterwards
			dat.setUint8(0, connection.handshake.unshuffle[dat.getUint8(0)]);

			const now = performance.now();
			let o = 1;
			switch (dat.getUint8(0)) {
				case 0x10: {
					// world update
					// carry forward record frames
					if (settings.synchronization === "flawless") {
						for (const key of /** @type {const} */ (["cells", "pellets"])) {
							for (const cell of world[key].values()) {
								const record = cell.views.get(view);
								if (!record) continue;

								record.frames.unshift(record.frames[0]);
								for (let i = 12, l = record.frames.length; i < l; ++i) record.frames.pop();
							}
						}
					}

					// (a) : eat
					const killCount = dat.getUint16(o, true);
					o += 2;
					for (let i = 0; i < killCount; ++i) {
						const killerId = dat.getUint32(o, true);
						const killedId = dat.getUint32(o + 4, true);
						o += 8;

						let pellet = true;
						let killed = world.pellets.get(killedId) ?? ((pellet = false), world.cells.get(killedId));
						if (!killed) continue;

						const record = killed.views.get(view);
						if (!record) continue;

						const frame = record.frames[0];
						// update interpolation using old targets
						const xyr = world.xyr(record.frames[0], record, undefined, undefined, pellet, now);
						record.ox = xyr.x;
						record.oy = xyr.y;
						record.or = xyr.r;
						record.jr = xyr.jr;
						record.a = xyr.a;
						record.updated = now;

						// update new targets (and dead-ness)
						record.frames[0] = {
							nx: frame.nx,
							ny: frame.ny,
							nr: frame.nr,
							born: frame.born,
							deadAt: now,
							deadTo: killerId,
						};

						if (pellet && vision.owned.has(killerId)) {
							++world.stats.foodEaten;
							net.food(view); // dumbass quest code go brrr
						}
					}

					// (b) : add, upd
					do {
						const id = dat.getUint32(o, true);
						o += 4;
						if (id === 0) break;

						const x = dat.getInt16(o, true);
						const y = dat.getInt16(o + 2, true);
						const r = dat.getUint16(o + 4, true);
						const flags = dat.getUint8(o + 6);
						// (void 1 byte, "isUpdate")
						// (void 1 byte, "isPlayer")
						const sub = !!dat.getUint8(o + 9);
						o += 10;

						let clan;
						[clan, o] = aux.readZTString(dat, o);

						/** @type {[number, number, number] | undefined} */
						let rgb;
						if (flags & 0x02) {
							// update color
							rgb = [dat.getUint8(o++) / 255, dat.getUint8(o++) / 255, dat.getUint8(o++) / 255];
						}

						/** @type {string | undefined} */
						let skin;
						if (flags & 0x04) {
							// update skin
							[skin, o] = aux.readZTString(dat, o);
							skin = aux.parseSkin(skin);
						}

						/** @type {string | undefined} */
						let name;
						if (flags & 0x08) {
							// update name
							[name, o] = aux.readZTString(dat, o);
							name = aux.parseName(name);
							if (name) render.textFromCache(name, sub); // make sure the texture is ready on render
						}

						const jagged = !!(flags & 0x11); // spiked or agitated
						const eject = !!(flags & 0x20);
						const pellet = r <= 40 && !eject; // tourney servers have bigger pellets (r=40)
						const cell = (pellet ? world.pellets : world.cells).get(id);
						const record = cell?.views.get(view);
						if (record) {
							const frame = record.frames[0];
							if (frame.deadAt === undefined) {
								// update interpolation using old targets
								const xyr = world.xyr(record.frames[0], record, undefined, undefined, pellet, now);
								record.ox = xyr.x;
								record.oy = xyr.y;
								record.or = xyr.r;
								record.jr = xyr.jr;
								record.a = xyr.a;
							} else {
								// cell just reappeared, discard all old data
								record.ox = x;
								record.oy = y;
								record.or = r;
								record.jr = r;
								record.a = 0;
							}

							record.updated = now;

							// update target frame
							record.frames[0] = {
								nx: x,
								ny: y,
								nr: r,
								born: frame.born,
								deadAt: undefined,
								deadTo: -1,
							};

							// update desc
							if (name !== undefined) record.name = name;
							if (skin !== undefined) record.skin = skin;
							if (rgb !== undefined) record.rgb = rgb;
							record.clan = clan;
							record.jagged = jagged;
							record.eject = eject;
						} else {
							/** @type {CellRecord} */
							const record = {
								ox: x,
								oy: y,
								or: r,
								jr: r,
								a: 0,
								updated: now,
								frames: [
									{
										nx: x,
										ny: y,
										nr: r,
										born: now,
										deadAt: undefined,
										deadTo: -1,
									},
								],
								name: name ?? "",
								skin: skin ?? "",
								sub,
								clan,
								rgb: rgb ?? [0.5, 0.5, 0.5],
								jagged,
								eject,
							};
							if (cell) {
								cell.views.set(view, record);
							} else {
								(pellet ? world.pellets : world.cells).set(id, {
									id,
									merged: undefined,
									model: undefined,
									views: new Map([[view, record]]),
								});
							}

							if (settings.synchronization === "latest" && !pellet && !eject && rgb) {
								// 'latest' requires us to predict which cells we will own
								// a name + color check should be enough
								/** @type {CellDescription | undefined} */
								let base;
								for (const id of vision.owned) {
									const desc = world.cells.get(id)?.views.get(view);
									if (!desc || desc.frames[0].deadAt !== undefined) continue;
									base = desc;
									break;
								}

								if (base && name === base.name && rgb[0] === base.rgb[0] && rgb[1] === base.rgb[1] && rgb[2] === base.rgb[2]) {
									vision.owned.add(id);
								}
							}
						}
					} while (true);

					// (c) : del
					const deleteCount = dat.getUint16(o, true);
					o += 2;
					for (let i = 0; i < deleteCount; ++i) {
						const deletedId = dat.getUint32(o, true);
						o += 4;

						const record = (world.pellets.get(deletedId) ?? world.cells.get(deletedId))?.views.get(view);
						if (!record) continue;

						const frame = record.frames[0];
						if (frame.deadAt !== undefined) continue;
						record.frames[0] = {
							nx: frame.nx,
							ny: frame.ny,
							nr: frame.nr,
							born: frame.born,
							deadAt: now,
							deadTo: -1,
						};
						// no interpolation stuff is updated because the target positions won't be changed,
						// unlike on eat where nx and ny are set to the killer's
					}

					// (d) : finalize, upload data
					world.merge();
					world.clean();
					render.upload(true);

					// (e) : clear own cells that don't exist anymore (NOT on world.clean!)
					for (const id of vision.owned) {
						const cell = world.cells.get(id);
						if (!cell) {
							vision.owned.delete(id);
							continue;
						}
						const record = cell?.views.get(view);

						if (record && record.frames[0].deadAt === undefined && connection.playBlock?.state === "joining") {
							connection.playBlock = undefined;
						}
					}

					ui.deathScreen.check();
					break;
				}

				case 0x11: {
					// update camera pos
					vision.camera.tx = dat.getFloat32(o, true);
					vision.camera.ty = dat.getFloat32(o + 4, true);
					vision.camera.tscale = dat.getFloat32(o + 8, true);
					break;
				}

				case 0x12: {
					// delete all cells
					// happens every time you respawn
					if (connection.playBlock?.state === "leaving") connection.playBlock.state = "joining";
					// the server won't respond to pings if you aren't in a world, and we don't want to show '????'
					// unless there's actually a problem
					connection.pinged = undefined;

					// DO NOT just clear the maps! when respawning, OgarII will not resend cell data if we spawn
					// nearby.
					for (const key of /** @type {const} */ (["cells", "pellets"])) {
						for (const cell of world[key].values()) {
							const record = cell.views.get(view);
							if (!record) continue;

							const frame = record.frames[0];
							if (settings.synchronization === "flawless") {
								record.frames.unshift({
									nx: frame.nx,
									ny: frame.ny,
									nr: frame.nr,
									born: frame.born,
									deadAt: frame.deadAt ?? now,
									deadTo: frame.deadTo || -1,
								});
							} else {
								const frameWritable = /** @type {CellFrameWritable} */ (frame);
								frameWritable.deadAt ??= now;
								frameWritable.deadTo ||= -1;
							}
						}
					}
					world.merge();
					render.upload(true);
					// passthrough
				}
				case 0x14: {
					// delete my cells
					// only reset spawn time if no other tab is alive.
					// this could be cheated (if you alternate respawning your tabs, for example) but i don't think
					// multiboxers ever see the stats menu anyway
					if (!world.alive()) world.stats.spawnedAt = undefined;
					ui.deathScreen.hide(); // don't trigger death screen on respawn
					break;
				}

				case 0x20: {
					// new owned cell
					// check if this is the first owned cell
					let first = true;
					let firstThis = true;
					for (const [otherView, otherVision] of world.views) {
						for (const id of otherVision.owned) {
							const record = world.cells.get(id)?.views.get(otherView);
							if (!record) continue;
							const frame = record.frames[0];
							if (frame.deadAt !== undefined) continue;

							first = false;
							if (otherVision === vision) {
								firstThis = false;
								break;
							}
						}
					}
					if (first) world.stats.spawnedAt = now;
					if (firstThis) vision.spawned = now;

					vision.owned.add(dat.getUint32(o, true));
					break;
				}

				case 0x31: {
					// ffa leaderboard list
					const lb = [];
					const count = dat.getUint32(o, true);
					o += 4;

					/** @type {number | undefined} */
					let myPosition;
					for (let i = 0; i < count; ++i) {
						const me = !!dat.getUint32(o, true);
						o += 4;

						let name;
						[name, o] = aux.readZTString(dat, o);
						name = aux.parseName(name);

						// why this is copied into every leaderboard entry is beyond my understanding
						myPosition = dat.getUint32(o, true);
						const sub = !!dat.getUint32(o + 4, true);
						o += 8;

						lb.push({ name, sub, me, place: undefined });
					}

					if (myPosition) {
						// myPosition could be zero
						if (myPosition - 1 >= lb.length) {
							const nick = input.nick[world.multis.indexOf(view) || 0].value;
							lb.push({
								me: true,
								name: aux.parseName(nick),
								place: myPosition,
								sub: false, // doesn't matter
							});
						}

						world.stats.highestPosition = Math.min(world.stats.highestPosition, myPosition);
					}

					vision.leaderboard = lb;
					break;
				}

				case 0x40: {
					// border update
					vision.border = {
						l: dat.getFloat64(o, true),
						t: dat.getFloat64(o + 8, true),
						r: dat.getFloat64(o + 16, true),
						b: dat.getFloat64(o + 24, true),
					};
					break;
				}

				case 0x63: {
					// chat message
					// only handle non-server chat messages on the primary tab, to prevent duplicate messages
					const flags = dat.getUint8(o++);
					const server = flags & 0x80;
					if (view !== world.viewId.primary && !server) return; // skip sigmod processing too
					const rgb =
						/** @type {[number, number, number, number]} */
						([dat.getUint8(o++) / 255, dat.getUint8(o++) / 255, dat.getUint8(o++) / 255, 1]);

					let name;
					[name, o] = aux.readZTString(dat, o);
					let msg;
					[msg, o] = aux.readZTString(dat, o);
					ui.chat.add(name, rgb, msg, !!(flags & 0x80));
					break;
				}

				case 0xb4: {
					// incorrect password alert
					ui.error("Password is incorrect");
					break;
				}

				case 0xfe: {
					// server stats (in response to a ping)
					let statString;
					[statString, o] = aux.readZTString(dat, o);
					vision.stats = JSON.parse(statString);
					if (connection.pinged !== undefined) connection.latency = now - connection.pinged;
					connection.pinged = undefined;
					break;
				}
			}

			sigmod.handleMessage?.(dat);
		});
		ws.addEventListener("open", () => {
			establishedCallback?.();
			establishedCallback = undefined;

			const connection = net.connections.get(view);
			const vision = world.views.get(view);
			if (!connection || !vision) return ws.close();

			ui.captcha.remove(view);

			connection.rejections = 0;
			connection.retries = 0;

			vision.camera.x = vision.camera.tx = 0;
			vision.camera.y = vision.camera.ty = 0;
			vision.camera.scale = input.zoom;
			vision.camera.tscale = 1;
			ws.send(aux.textEncoder.encode("SIG 0.0.1\x00"));
		});

		return ws;
	};

	// ping loop
	setInterval(() => {
		for (const connection of net.connections.values()) {
			if (!connection.handshake || connection.ws?.readyState !== WebSocket.OPEN) continue;
			if (connection.pinged !== undefined) connection.latency = -1; // display '????ms'
			connection.pinged = performance.now();
			connection.ws.send(connection.handshake.shuffle.slice(0xfe, 0xfe + 1));
		}
	}, 2000);

	// #2 : define helper functions
	/** @type {HTMLSelectElement | null} */
	const gamemode = document.querySelector("#gamemode");
	/** @type {HTMLOptionElement | null} */
	const firstGamemode = document.querySelector("#gamemode option");
	net.url = () => {
		if (location.search.startsWith("?ip=")) return location.search.slice("?ip=".length);
		else return "wss://" + (gamemode?.value || firstGamemode?.value || "ca0.sigmally.com/ws/");
	};

	/** @param {symbol} view */
	net.respawnable = view => {
		const vision = world.views.get(view);
		const con = net.connections.get(view);
		if (!vision || !con?.ws) return false;

		// only allow respawns on localhost (players on personal private servers can simply append `localhost`)
		return world.score(view) < 5500 || con.ws.url.includes("localhost");
	};

	// disconnect if a different gamemode is selected
	// an interval is preferred because the game can apply its gamemode setting *after* connecting without
	// triggering any events
	setInterval(() => {
		for (const connection of net.connections.values()) {
			if (!connection.ws) continue;
			if (connection.ws.readyState !== WebSocket.CONNECTING && connection.ws.readyState !== WebSocket.OPEN) continue;
			if (connection.ws.url === net.url()) continue;
			connection.ws.close();
		}
	}, 200);

	/**
	 * @param {symbol} view
	 * @param {number} opcode
	 * @param {object} data
	 */
	const sendJson = (view, opcode, data) => {
		// must check readyState as a weboscket might be in the 'CLOSING' state (so annoying!)
		const connection = net.connections.get(view);
		if (!connection?.handshake || connection.ws?.readyState !== WebSocket.OPEN) return;
		const dataBuf = aux.textEncoder.encode(JSON.stringify(data));
		const dat = new DataView(new ArrayBuffer(dataBuf.byteLength + 2));

		dat.setUint8(0, connection.handshake.shuffle[opcode]);
		for (let i = 0; i < dataBuf.byteLength; ++i) {
			dat.setUint8(1 + i, dataBuf[i]);
		}
		connection.ws.send(dat);
	};

	// #5 : export input functions
	/**
	 * @param {symbol} view
	 * @param {number} x
	 * @param {number} y
	 */
	net.move = (view, x, y) => {
		const connection = net.connections.get(view);
		if (!connection?.handshake || connection.ws?.readyState !== WebSocket.OPEN) return;
		const dat = new DataView(new ArrayBuffer(13));

		dat.setUint8(0, connection.handshake.shuffle[0x10]);
		dat.setInt32(1, x, true);
		dat.setInt32(5, y, true);
		connection.ws.send(dat);
	};

	/** @param {number} opcode */
	const bindOpcode = opcode => /** @param {symbol} view */ view => {
		const connection = net.connections.get(view);
		if (!connection?.handshake || connection.ws?.readyState !== WebSocket.OPEN) return;
		connection.ws.send(connection.handshake.shuffle.slice(opcode, opcode + 1));
	};
	net.w = bindOpcode(21);
	net.qdown = bindOpcode(18);
	net.qup = bindOpcode(19);
	net.split = bindOpcode(17);
	// quests
	net.food = bindOpcode(0xc0);
	net.time = bindOpcode(0xbf);

	// reversed argument order for sigmod compatibility
	/**
	 * @param {string} msg
	 * @param {symbol=} view
	 */
	net.chat = (msg, view = world.selected) => {
		const connection = net.connections.get(view);
		if (!connection?.handshake || connection.ws?.readyState !== WebSocket.OPEN) return;

		if (msg.toLowerCase().startsWith("/leaveworld") && !net.respawnable(view)) return; // prevent abuse

		const msgBuf = aux.textEncoder.encode(msg);
		const dat = new DataView(new ArrayBuffer(msgBuf.byteLength + 3));

		dat.setUint8(0, connection.handshake.shuffle[0x63]);
		// skip flags
		for (let i = 0; i < msgBuf.byteLength; ++i) {
			dat.setUint8(2 + i, msgBuf[i]);
		}
		connection.ws.send(dat);
	};

	/**
	 * @param {symbol} view
	 * @param {{ name: string, skin: string, [x: string]: any }} data
	 */
	net.play = (view, data) => {
		const connection = net.connections.get(view);
		const now = performance.now();
		if (!data.state) {
			if (!connection || (connection.playBlock !== undefined && now - connection.playBlock.started < 750) || world.score(view) > 0) return;
			connection.playBlock = { state: "joining", started: now };
			ui.deathScreen.hide();
		}
		sendJson(view, 0x00, data);
	};

	/**
	 * @param {symbol} view
	 * @param {{ name: string, skin: string, [x: string]: any }} data
	 */
	net.respawn = (view, data) => {
		const connection = net.connections.get(view);
		if (!connection?.handshake || connection.ws?.readyState !== WebSocket.OPEN) return;

		const now = performance.now();
		if (connection.playBlock !== undefined && now - connection.playBlock.started < 750) return;

		const score = world.score(view);
		if (score <= 0) {
			// if dead, no need to leave+rejoin the world
			net.play(view, data);
			return;
		} else if (!net.respawnable(view)) return;

		if (settings.blockNearbyRespawns) {
			const vision = world.views.get(view);
			if (!vision?.border) return;

			world.cameras(now);
			const { l, r, t, b } = vision.border;

			for (const [otherView, otherVision] of world.views) {
				if (otherView === view || world.score(otherView) <= 0) continue;

				// block respawns if both views are close enough (minimap squares give too large of a threshold)
				const d = Math.hypot(vision.camera.tx - otherVision.camera.tx, vision.camera.ty - otherVision.camera.ty);
				if (d <= Math.min(r - l, b - t) / 4) return;
			}
		}

		connection.playBlock = { state: "leaving", started: now };
		net.chat("/leaveworld", view); // immediately remove from world, which removes all player cells
		sendJson(view, 0x00, data); // enqueue into matchmaker (/joinworld is not available if dead)
		setTimeout(() => {
			// wait until Matchmaker.update() puts us into a world
			sendJson(view, 0x00, data); // spawn
		}, 60); // = 40ms (1 tick) + 20ms (margin of error)
	};

	// create initial connection
	world.create(world.viewId.primary);
	net.create(world.viewId.primary);
	let lastChangedSpectate = -Infinity;
	setInterval(() => {
		if (!settings.multibox && !settings.nbox) {
			world.selected = world.viewId.primary;
			ui.captcha.reposition();
			ui.linesplit.update();
		}

		if (settings.spectator) {
			const vision = world.create(world.viewId.spectate);
			net.create(world.viewId.spectate);
			net.play(world.viewId.spectate, { name: "", skin: "", clan: aux.userData?.clan, state: 2 });

			// only press Q to toggle once in a while, in case ping is above 200
			const now = performance.now();
			if (now - lastChangedSpectate > 1000) {
				if (vision.camera.tscale > 0.39) {
					// when roaming, the spectate scale is set to ~0.4
					net.qdown(world.viewId.spectate);
					lastChangedSpectate = now;
				}
			} else {
				net.qup(world.viewId.spectate); // doubly serves as anti-afk
			}
		} else {
			const con = net.connections.get(world.viewId.spectate);
			if (con?.ws && con?.ws.readyState !== WebSocket.CLOSED && con?.ws.readyState !== WebSocket.CLOSING) {
				con?.ws.close();
			}
			net.connections.delete(world.viewId.spectate);
			world.views.delete(world.viewId.spectate);
			input.views.delete(world.viewId.spectate);

			for (const key of /** @type {const} */ (["cells", "pellets"])) {
				for (const [id, resolution] of world[key]) {
					resolution.views.delete(world.viewId.spectate);
					if (resolution.views.size === 0) world[key].delete(id);
				}
			}
		}
	}, 200);

	// dumbass quest code go brrr
	setInterval(() => {
		for (const view of net.connections.keys()) net.time(view);
	}, 1000);

	return net;
})();
