"use strict";
// ==UserScript==
// @name         Script Teste
// @namespace    script-test
// @version      1.0.0
// <AUTHOR>
// @description  Um script de teste para demonstrar funcionalidades avançadas do Tampermonkey
// @license MIT
// @match        https://agariobr.com.br/*
// @grant        none
// @run-at       document-start
// ==/UserScript==
Object.defineProperty(exports, "__esModule", { value: true });
exports.NetworkOpcode = void 0;
// ===== CONFIGURAÇÕES =====
const CONFIG = {
    SCRIPT_NAME: "teste",
    VERSION: "1.0.30",
    STORAGE_KEYS: {
        SETTINGS: "settings",
    },
};
var NetworkOpcode;
(function (NetworkOpcode) {
    NetworkOpcode[NetworkOpcode["WorldUpdate"] = 16] = "WorldUpdate";
    NetworkOpcode[NetworkOpcode["PositionUpdate"] = 17] = "PositionUpdate";
    NetworkOpcode[NetworkOpcode["Leaderboard"] = 18] = "Leaderboard";
    NetworkOpcode[NetworkOpcode["ClearCells"] = 20] = "ClearCells";
    NetworkOpcode[NetworkOpcode["RemoveCells"] = 32] = "RemoveCells";
    NetworkOpcode[NetworkOpcode["MapBorders"] = 48] = "MapBorders";
    NetworkOpcode[NetworkOpcode["OwnCells"] = 64] = "OwnCells";
    NetworkOpcode[NetworkOpcode["ChatMessage"] = 99] = "ChatMessage";
    NetworkOpcode[NetworkOpcode["ServerStats"] = 100] = "ServerStats";
    NetworkOpcode[NetworkOpcode["PartyInfo"] = 101] = "PartyInfo";
    NetworkOpcode[NetworkOpcode["PartyUpdate"] = 102] = "PartyUpdate";
    NetworkOpcode[NetworkOpcode["Minimap"] = 103] = "Minimap";
    NetworkOpcode[NetworkOpcode["PasswordError"] = 180] = "PasswordError";
})(NetworkOpcode || (exports.NetworkOpcode = NetworkOpcode = {}));
// ===== SERVIÇOS DE ARMAZENAMENTO ESSENCIAL =====
class StorageService {
    static get(key) {
        try {
            const item = localStorage.getItem(key);
            return item ? JSON.parse(item) : null;
        }
        catch (_a) {
            return null;
        }
    }
    static setJSON(key, value) {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        }
        catch (_a) {
            return false;
        }
    }
    static remove(key) {
        try {
            localStorage.removeItem(key);
            return true;
        }
        catch (_a) {
            return false;
        }
    }
}
class DOMUtilities {
    static createElement(tag, options = {}) {
        const element = document.createElement(tag);
        if (options.className)
            element.className = options.className;
        if (options.textContent)
            element.textContent = options.textContent;
        if (options.styles)
            Object.assign(element.style, options.styles);
        if (options.attributes)
            Object.entries(options.attributes).forEach(([key, value]) => {
                element.setAttribute(key, value);
            });
        if (options.eventListeners)
            Object.entries(options.eventListeners).forEach(([event, listener]) => {
                element.addEventListener(event, listener);
            });
        return element;
    }
    static removeElement(element) {
        if (element && element.parentNode) {
            element.parentNode.removeChild(element);
            return true;
        }
        return false;
    }
}
class SettingsStore {
    constructor() {
        this.settings = {};
        this.storageKey = CONFIG.STORAGE_KEYS.SETTINGS;
        this.loadSettings();
    }
    static getInstance() {
        if (!SettingsStore.instance) {
            SettingsStore.instance = new SettingsStore();
        }
        return SettingsStore.instance;
    }
    loadSettings() {
        const savedSettings = StorageService.get(this.storageKey);
        if (savedSettings && typeof savedSettings === "object") {
            this.settings = savedSettings;
        }
    }
    saveSettings() {
        return StorageService.setJSON(this.storageKey, this.settings);
    }
    getAllSettings() {
        return Object.assign({}, this.settings);
    }
    getSetting(key) {
        return this.settings[key] || null;
    }
    setSetting(key, value) {
        this.settings[key] = value;
        return this.saveSettings();
    }
}
class KeyBindManager {
    constructor() {
        this.keyBindings = new Map();
        this.setupGlobalListener();
    }
    static getInstance() {
        if (!KeyBindManager.instance) {
            KeyBindManager.instance = new KeyBindManager();
        }
        return KeyBindManager.instance;
    }
    setupGlobalListener() {
        document.addEventListener("keydown", event => {
            const key = event.key.toLowerCase();
            const binding = this.keyBindings.get(key);
            if (binding) {
                const result = binding.handler(event);
                if (result !== false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
            }
        });
    }
    register(binding) {
        const key = binding.key.toLowerCase();
        this.keyBindings.set(key, binding);
        return true;
    }
    listBindings() {
        return Array.from(this.keyBindings.values());
    }
}
// ===== GERENCIADOR DE REDE AVANÇADO =====
class NetworkManager {
    constructor() {
        this.textEncoder = new TextEncoder();
        this.captchaPostQueue = Promise.resolve();
        // Armazenamento de conexões por visualização
        this.connections = new Map();
        // Buffer de mensagens para debug e análise
        this.messageBuffer = [];
        this.originalWebSocket = window.WebSocket;
        this.interceptWebSocket();
        this.startPingLoop();
        this.startGamemodeMonitoring();
        this.startQuestTimers();
    }
    static getInstance() {
        if (!NetworkManager.instance) {
            NetworkManager.instance = new NetworkManager();
        }
        return NetworkManager.instance;
    }
    // ===== INTERCEPTAÇÃO DE WEBSOCKET =====
    // Intercepta construção de WebSockets para servidores do jogo
    interceptWebSocket() {
        const self = this;
        window.WebSocket = class extends self.originalWebSocket {
            constructor(url, protocols) {
                super(url, protocols);
                const urlString = url.toString();
                // Detecta conexões para servidores de jogo
                if (urlString.includes("agariobr.com.br") || urlString.includes("sigmally.com")) {
                    console.log("🔗 Interceptando WebSocket para:", urlString);
                    self.handleNewConnection(this, urlString);
                }
            }
        };
        Object.setPrototypeOf(window.WebSocket.prototype, this.originalWebSocket.prototype);
        Object.setPrototypeOf(window.WebSocket, this.originalWebSocket);
    }
    // Processa nova conexão WebSocket detectada
    handleNewConnection(socket, url) {
        console.log("🔗 Nova conexão WebSocket detectada:", url);
        // Encontra ou cria uma visualização para esta conexão
        const view = this.findOrCreateView();
        const connection = this.connections.get(view) || this.createConnection(view);
        connection.ws = socket;
        this.attachSocketListeners(socket, view);
    }
    // ===== GERENCIAMENTO DE CONEXÕES =====
    // Cria uma nova conexão para uma visualização
    createConnection(view) {
        if (this.connections.has(view))
            return this.connections.get(view);
        const connection = {
            handshake: undefined,
            latency: undefined,
            pinged: undefined,
            playBlock: undefined,
            rejections: 0,
            retries: 0,
            ws: this.connect(view),
        };
        this.connections.set(view, connection);
        return connection;
    }
    // Estabelece conexão WebSocket para uma visualização
    connect(view, establishedCallback) {
        const connection = this.connections.get(view);
        if (connection === null || connection === void 0 ? void 0 : connection.ws)
            return connection.ws; // já está sendo processada
        const realUrl = this.getGameUrl();
        // Previne detecção de sigmod usando URL fake
        const fakeUrl = { includes: () => false, toString: () => realUrl };
        let ws;
        try {
            ws = new this.originalWebSocket(fakeUrl);
        }
        catch (err) {
            console.error("💥 Erro ao criar WebSocket:", err);
            this.showConnectionError();
            return;
        }
        // Atualiza referência da conexão
        if (connection)
            connection.ws = ws;
        ws.binaryType = "arraybuffer";
        this.attachSocketListeners(ws, view, establishedCallback);
        return ws;
    }
    // Adiciona listeners de eventos do WebSocket
    attachSocketListeners(socket, view, establishedCallback) {
        const originalSend = socket.send.bind(socket);
        // Intercepta mensagens enviadas
        socket.send = (data) => {
            this.logMessage(view, data, "sent");
            return originalSend(data);
        };
        // Processa eventos do WebSocket
        socket.addEventListener("open", () => this.handleSocketOpen(socket, view, establishedCallback));
        socket.addEventListener("close", event => this.handleSocketClose(event, view, establishedCallback));
        socket.addEventListener("error", event => this.handleSocketError(event, view));
        socket.addEventListener("message", event => this.handleSocketMessage(event, view));
    }
    // ===== MANIPULADORES DE EVENTOS =====
    // Conexão WebSocket aberta
    handleSocketOpen(socket, view, establishedCallback) {
        console.log("✅ WebSocket conectado:", socket.url);
        establishedCallback === null || establishedCallback === void 0 ? void 0 : establishedCallback();
        const connection = this.connections.get(view);
        if (!connection)
            return socket.close();
        // Reset de contadores de erro
        connection.rejections = 0;
        connection.retries = 0;
        // Envia handshake inicial
        socket.send(this.textEncoder.encode("SIG 0.0.1\x00"));
    }
    // Conexão WebSocket fechada
    handleSocketClose(event, view, establishedCallback) {
        console.warn("❌ WebSocket fechado:", event.code, event.reason);
        establishedCallback === null || establishedCallback === void 0 ? void 0 : establishedCallback();
        const connection = this.connections.get(view);
        if (!connection)
            return;
        // Reset do estado da conexão
        this.resetConnectionState(connection, view);
        // Tenta reconectar com estratégia baseada em captcha
        this.attemptReconnection(view, connection);
    }
    // Erro na conexão WebSocket
    handleSocketError(event, view) {
        console.error("💥 Erro no WebSocket:", event);
    }
    // Mensagem recebida via WebSocket
    handleSocketMessage(event, view) {
        const connection = this.connections.get(view);
        if (!connection)
            return;
        const data = new DataView(event.data);
        this.logMessage(view, event.data, "received");
        // Processa handshake se ainda não foi estabelecido
        if (!connection.handshake) {
            this.processHandshake(data, connection);
            return;
        }
        // Decodifica o opcode da mensagem
        const originalOpcode = data.getUint8(0);
        const opcode = connection.handshake.unshuffle[originalOpcode];
        data.setUint8(0, opcode);
        // Processa mensagem baseada no opcode
        this.processGameMessage(data, opcode, view, connection);
    }
    // ===== PROCESSAMENTO DE MENSAGENS =====
    // Processa handshake inicial do servidor
    processHandshake(data, connection) {
        let offset = 10; // Pula versão "SIG 0.0.1\0"
        const shuffle = new Uint8Array(256);
        const unshuffle = new Uint8Array(256);
        for (let i = 0; i < 256; i++) {
            const shuffled = data.getUint8(offset + i);
            shuffle[i] = shuffled;
            unshuffle[shuffled] = i;
        }
        connection.handshake = { shuffle, unshuffle };
        console.log("🤝 Handshake estabelecido com sucesso");
    }
    // Processa mensagens do jogo baseadas no opcode
    processGameMessage(data, opcode, view, connection) {
        const now = performance.now();
        switch (opcode) {
            case NetworkOpcode.WorldUpdate:
                console.log("🌍 Atualização do mundo recebida");
                this.processWorldUpdate(data, view, now);
                break;
            case NetworkOpcode.PositionUpdate:
                console.log("📍 Atualização de posição recebida");
                this.processPositionUpdate(data, view);
                break;
            case NetworkOpcode.Leaderboard:
                console.log("🏆 Leaderboard recebida");
                this.processLeaderboard(data, view);
                break;
            case NetworkOpcode.ClearCells:
                console.log("🧹 Limpeza de células recebida");
                this.processClearCells(data, view);
                break;
            case NetworkOpcode.RemoveCells:
                console.log("❌ Remoção de células recebida");
                this.processRemoveCells(data, view);
                break;
            case NetworkOpcode.MapBorders:
                console.log("🗺️ Limites do mapa recebidos");
                this.processMapBorders(data, view);
                break;
            case NetworkOpcode.OwnCells:
                console.log("👤 Células próprias recebidas");
                this.processOwnCells(data, view, now);
                break;
            case NetworkOpcode.ChatMessage:
                console.log("💬 Mensagem de chat recebida");
                this.processChatMessage(data, view);
                break;
            case NetworkOpcode.ServerStats:
                console.log("📊 Estatísticas do servidor recebidas");
                this.processServerStats(data, view, connection, now);
                break;
            case NetworkOpcode.PasswordError:
                console.log("🔒 Erro de senha recebido");
                this.processPasswordError();
                break;
            default:
                console.log(`❓ Opcode desconhecido: 0x${opcode.toString(16)}`);
        }
    }
    // ===== PROCESSADORES DE MENSAGENS ESPECÍFICAS =====
    processWorldUpdate(data, view, now) {
        // Implementação simplificada - processaria células, pellets, etc.
        console.log("Processando atualização do mundo...");
    }
    processPositionUpdate(data, view) {
        const x = data.getFloat32(1, true);
        const y = data.getFloat32(5, true);
        const scale = data.getFloat32(9, true);
        console.log(`Nova posição de câmera: (${x}, ${y}) scale: ${scale}`);
    }
    processLeaderboard(data, view) {
        console.log("Atualizando leaderboard...");
    }
    processClearCells(data, view) {
        console.log("Limpando todas as células...");
    }
    processRemoveCells(data, view) {
        console.log("Removendo células específicas...");
    }
    processMapBorders(data, view) {
        const left = data.getFloat64(1, true);
        const top = data.getFloat64(9, true);
        const right = data.getFloat64(17, true);
        const bottom = data.getFloat64(25, true);
        console.log(`Limites do mapa: L:${left} T:${top} R:${right} B:${bottom}`);
    }
    processOwnCells(data, view, now) {
        const cellId = data.getUint32(1, true);
        console.log(`Nova célula própria: ${cellId}`);
    }
    processChatMessage(data, view) {
        console.log("Nova mensagem de chat recebida");
    }
    processServerStats(data, view, connection, now) {
        if (connection.pinged !== undefined) {
            connection.latency = now - connection.pinged;
            console.log(`🏓 Latência atualizada: ${connection.latency}ms`);
        }
        connection.pinged = undefined;
    }
    processPasswordError() {
        console.error("🔒 Senha incorreta!");
    }
    // ===== UTILITÁRIOS E HELPERS =====
    // Encontra ou cria uma visualização para nova conexão
    findOrCreateView() {
        // Retorna uma visualização existente ou cria nova
        // Por simplicidade, usa sempre a mesma
        return Symbol("primary-view");
    }
    // Obtém URL do servidor de jogo
    getGameUrl() {
        const gamemode = document.querySelector("#gamemode");
        const firstGamemode = document.querySelector("#gamemode option");
        if (location.search.startsWith("?ip=")) {
            return location.search.slice("?ip=".length);
        }
        return "wss://" + ((gamemode === null || gamemode === void 0 ? void 0 : gamemode.value) || (firstGamemode === null || firstGamemode === void 0 ? void 0 : firstGamemode.value) || "ca0.sigmally.com/ws/");
    }
    // Mostra erro de conexão para o usuário
    showConnectionError() {
        console.error("❌ Erro ao conectar: Verifique o endereço do servidor ou configurações de segurança");
    }
    // Reset do estado da conexão após desconexão
    resetConnectionState(connection, view) {
        connection.handshake = undefined;
        connection.latency = undefined;
        connection.pinged = undefined;
        connection.playBlock = undefined;
        connection.rejections++;
        if (connection.retries > 0)
            connection.retries--;
        connection.ws = undefined;
    }
    // Tentativa de reconexão com estratégia baseada em captcha
    attemptReconnection(view, connection) {
        if (connection.retries > 0) {
            setTimeout(() => this.connect(view), 500);
        }
        else {
            // Implementaria lógica de captcha aqui
            connection.retries = 3;
            setTimeout(() => this.connect(view), connection.rejections >= 5 ? 5000 : 500);
        }
    }
    // Registra mensagem no buffer para análise
    logMessage(view, data, type) {
        this.messageBuffer.push({
            timestamp: performance.now(),
            view,
            data,
            type,
        });
        // Limita tamanho do buffer
        if (this.messageBuffer.length > 1000) {
            this.messageBuffer = this.messageBuffer.slice(-500);
        }
    }
    // ===== LOOPS E TIMERS =====
    // Loop de ping para medir latência
    startPingLoop() {
        setInterval(() => {
            var _a;
            for (const connection of this.connections.values()) {
                if (!connection.handshake || ((_a = connection.ws) === null || _a === void 0 ? void 0 : _a.readyState) !== WebSocket.OPEN)
                    continue;
                if (connection.pinged !== undefined) {
                    connection.latency = -1; // Exibe '????ms'
                }
                connection.pinged = performance.now();
                connection.ws.send(connection.handshake.shuffle.slice(0xfe, 0xfe + 1));
            }
        }, 2000);
    }
    // Monitora mudanças de gamemode para desconectar conexões antigas
    startGamemodeMonitoring() {
        setInterval(() => {
            const currentUrl = this.getGameUrl();
            for (const connection of this.connections.values()) {
                if (!connection.ws)
                    continue;
                if (connection.ws.readyState !== WebSocket.CONNECTING && connection.ws.readyState !== WebSocket.OPEN)
                    continue;
                if (connection.ws.url === currentUrl)
                    continue;
                connection.ws.close();
            }
        }, 200);
    }
    // Timers para quests (funcionalidade do jogo)
    startQuestTimers() {
        setInterval(() => {
            for (const view of this.connections.keys()) {
                this.sendQuestTime(view);
            }
        }, 1000);
    }
    // ===== API PÚBLICA =====
    // Envia comando de tempo para quest
    sendQuestTime(view) {
        this.sendOpcode(view, 0xbf);
    }
    // Envia opcode simples
    sendOpcode(view, opcode) {
        var _a;
        const connection = this.connections.get(view);
        if (!(connection === null || connection === void 0 ? void 0 : connection.handshake) || ((_a = connection.ws) === null || _a === void 0 ? void 0 : _a.readyState) !== WebSocket.OPEN)
            return;
        connection.ws.send(connection.handshake.shuffle.slice(opcode, opcode + 1));
    }
    // Obtém informações de todas as conexões
    getAllConnectionsInfo() {
        var _a, _b;
        const info = [];
        for (const [view, connection] of this.connections) {
            info.push({
                view,
                url: (_a = connection.ws) === null || _a === void 0 ? void 0 : _a.url,
                readyState: (_b = connection.ws) === null || _b === void 0 ? void 0 : _b.readyState,
                latency: connection.latency,
            });
        }
        return info;
    }
    // Obtém mensagens recentes para análise
    getRecentMessages(seconds = 10) {
        const cutoff = performance.now() - seconds * 1000;
        return this.messageBuffer.filter(msg => msg.timestamp > cutoff);
    }
    // Obtém estatísticas de rede
    getNetworkStats() {
        return {
            totalConnections: this.connections.size,
            activeConnections: Array.from(this.connections.values()).filter(c => { var _a; return ((_a = c.ws) === null || _a === void 0 ? void 0 : _a.readyState) === WebSocket.OPEN; }).length,
            totalMessages: this.messageBuffer.length,
            averageLatency: this.calculateAverageLatency(),
        };
    }
    // Calcula latência média das conexões ativas
    calculateAverageLatency() {
        const latencies = Array.from(this.connections.values())
            .map(c => c.latency)
            .filter(l => l !== undefined && l > 0);
        if (latencies.length === 0)
            return 0;
        return latencies.reduce((sum, lat) => sum + lat, 0) / latencies.length;
    }
}
// ===== CLASSE PRINCIPAL DO MUNDO DO JOGO =====
class World {
    constructor() {
        // Armazenamento principal de entidades do jogo
        this.cells = new Map();
        this.pellets = new Map();
        // Símbolos únicos para identificar diferentes abas/visualizações
        this.multis = Array.from({ length: 8 }, () => Symbol());
        // IDs das visualizações principais
        this.viewId = {
            primary: this.multis[0],
            secondary: this.multis[1],
            spectate: Symbol(),
        };
        // Visualização atualmente selecionada
        this.selected = this.viewId.primary;
        // Mapa de todas as visualizações ativas
        this.views = new Map();
        // Estado de sincronização entre abas
        this.synchronized = false;
        this.wasFlawlessSynchronized = false;
        this.lastClean = performance.now();
        // Estatísticas do jogador
        this.stats = {
            foodEaten: 0,
            highestPosition: 200,
            highestScore: 0,
            spawnedAt: undefined,
        };
        // Configurações simuladas (normalmente viriam de um objeto settings global)
        this.settings = {
            cameraMovement: "default",
            camera: "default",
            multibox: true,
            nbox: false,
            mergeCamera: true,
            cameraSpawnAnimation: true,
            cameraSmoothness: 10,
            autoZoom: true,
            synchronization: "flawless",
            drawDelay: 120,
            slowerJellyPhysics: false,
        };
    }
    // Padrão Singleton para garantir uma única instância
    static getInstance() {
        if (!World.instance) {
            World.instance = new World();
        }
        return World.instance;
    }
    // ===== MÉTODOS PÚBLICOS =====
    // Verifica se há células vivas em qualquer visualização
    isAlive() {
        var _a;
        for (const [view, vision] of this.views) {
            for (const id of vision.owned) {
                const cell = this.cells.get(id);
                // Se a célula não existe ainda, consideramos como viva
                if (!cell)
                    return true;
                const frame = (_a = cell.views.get(view)) === null || _a === void 0 ? void 0 : _a.frames[0];
                if ((frame === null || frame === void 0 ? void 0 : frame.deadAt) === undefined)
                    return true;
            }
        }
        return false;
    }
    // Calcula a câmera para uma visualização específica
    calculateSingleCamera(view, vision, weightExponent = 0, now = performance.now()) {
        var _a, _b;
        vision = vision !== null && vision !== void 0 ? vision : this.views.get(view);
        if (!vision) {
            return { mass: 0, scale: 1, sumX: 0, sumY: 0, weight: 0 };
        }
        let mass = 0;
        let r = 0;
        let sumX = 0;
        let sumY = 0;
        let weight = 0;
        // Processa todas as células possuídas por esta visualização
        for (const id of vision.owned) {
            const cell = this.cells.get(id);
            const frame = this.synchronized ? cell === null || cell === void 0 ? void 0 : cell.merged : (_a = cell === null || cell === void 0 ? void 0 : cell.views.get(view)) === null || _a === void 0 ? void 0 : _a.frames[0];
            const interp = this.synchronized ? cell === null || cell === void 0 ? void 0 : cell.merged : (_b = cell === null || cell === void 0 ? void 0 : cell.views.get(view)) === null || _b === void 0 ? void 0 : _b.frames[0];
            if (!frame || !interp)
                continue;
            // Não incluir células possuídas antes do respawn
            if (frame.born < vision.spawned)
                continue;
            if (this.settings.cameraMovement === "instant") {
                const xyr = this.calculatePosition(frame, interp, undefined, undefined, false, now);
                r += xyr.r * xyr.a;
                mass += ((xyr.r * xyr.r) / 100) * xyr.a;
                const cellWeight = xyr.a * Math.pow(xyr.r, weightExponent);
                sumX += xyr.x * cellWeight;
                sumY += xyr.y * cellWeight;
                weight += cellWeight;
            }
            else {
                // Movimento de câmera padrão
                if (frame.deadAt !== undefined)
                    continue;
                const xyr = this.calculatePosition(frame, interp, undefined, undefined, false, now);
                r += frame.nr;
                mass += (frame.nr * frame.nr) / 100;
                const cellWeight = Math.pow(frame.nr, weightExponent);
                sumX += xyr.x * cellWeight;
                sumY += xyr.y * cellWeight;
                weight += cellWeight;
            }
        }
        const scale = Math.pow(Math.min(64 / r, 1), 0.4);
        return { mass, scale, sumX, sumY, weight };
    }
    // Calcula e atualiza as posições de câmera para todas as visualizações
    updateCameras(now = performance.now()) {
        const weightExponent = this.settings.camera !== "default" ? 2 : 0;
        // Cria conjuntos disjuntos de todas as câmeras próximas
        const cameras = new Map();
        const sets = new Map();
        for (const [view, vision] of this.views) {
            cameras.set(view, this.calculateSingleCamera(view, vision, weightExponent, now));
            sets.set(view, new Set([view]));
        }
        // Calcula mesmo se as abas não forem realmente mescladas (para contornos multi)
        if (this.settings.multibox || this.settings.nbox) {
            this.mergeCameraSets(cameras, sets, now);
        }
        // Calcula e atualiza posições de câmera mescladas
        this.updateMergedCameras(cameras, sets, now);
    }
    // Cria ou retorna uma visualização existente
    createVision(view) {
        const existing = this.views.get(view);
        if (existing)
            return existing;
        const vision = {
            border: undefined,
            camera: {
                x: 0,
                tx: 0,
                y: 0,
                ty: 0,
                scale: 0,
                tscale: 0,
                merged: false,
                updated: performance.now() - 1,
            },
            leaderboard: [],
            owned: new Set(),
            spawned: -Infinity,
            stats: undefined,
            used: -Infinity,
        };
        this.views.set(view, vision);
        return vision;
    }
    // Sincroniza frames entre diferentes visualizações
    synchronizeViews() {
        if (this.wasFlawlessSynchronized && this.settings.synchronization !== "flawless")
            this.cleanupFrameHistory();
        if (!this.settings.synchronization || this.views.size <= 1) {
            this.resetSynchronization();
            return;
        }
        const now = performance.now();
        const indices = {};
        if (this.settings.synchronization === "flawless") {
            this.performFlawlessSynchronization(indices, now);
        }
        else {
            this.performLatestSynchronization(indices);
        }
        this.mergeFrames(indices, now);
        this.cleanupFrameHistory();
        this.updateSynchronizationState(now);
    }
    // Calcula pontuação total para uma visualização
    calculateScore(view) {
        var _a;
        let score = 0;
        const vision = this.views.get(view);
        if (!vision)
            return 0;
        for (const id of vision.owned) {
            const cell = this.cells.get(id);
            if (!cell)
                continue;
            const frame = this.synchronized ? cell.merged : (_a = cell.views.get(view)) === null || _a === void 0 ? void 0 : _a.frames[0];
            if (!frame || frame.deadAt !== undefined)
                continue;
            // Usa pontuação exata do servidor, sem interpolação
            score += (frame.nr * frame.nr) / 100;
        }
        return score;
    }
    // Calcula posição interpolada de uma célula
    calculatePosition(frame, interp, killerFrame, killerInterp, isPellet = false, now = performance.now()) {
        let nx = frame.nx;
        let ny = frame.ny;
        // Anima em direção à posição interpolada do assassino para suavidade extra
        if (killerFrame && killerInterp) {
            const killerXyr = this.calculatePosition(killerFrame, killerInterp, undefined, undefined, false, now);
            nx = killerXyr.x;
            ny = killerXyr.y;
        }
        let x, y, r, a;
        if (isPellet && frame.deadAt === undefined) {
            // Pellets não se movem suavemente
            x = nx;
            y = ny;
            r = frame.nr;
            a = 1;
        }
        else {
            // Interpolação suave para células
            let alpha = (now - interp.updated) / this.settings.drawDelay;
            alpha = Math.max(0, Math.min(1, alpha));
            x = interp.ox + (nx - interp.ox) * alpha;
            y = interp.oy + (ny - interp.oy) * alpha;
            r = interp.or + (frame.nr - interp.or) * alpha;
            const targetA = frame.deadAt !== undefined ? 0 : 1;
            a = interp.a + (targetA - interp.a) * alpha;
        }
        const dt = (now - interp.updated) / 1000;
        const jellyPhysicsSpeed = this.settings.slowerJellyPhysics ? 10 : 5;
        return {
            x,
            y,
            r,
            jr: this.exponentialEase(interp.jr, r, jellyPhysicsSpeed, dt),
            a,
        };
    }
    // Remove células mortas e invisíveis
    cleanupDeadCells() {
        const now = performance.now();
        if (now - this.lastClean < 200)
            return;
        this.lastClean = now;
        for (const collection of [this.cells, this.pellets]) {
            for (const [id, cell] of collection) {
                for (const [view, record] of cell.views) {
                    const firstFrame = record.frames[0];
                    const lastFrame = record.frames[record.frames.length - 1];
                    if (firstFrame.deadAt !== lastFrame.deadAt)
                        continue;
                    if (lastFrame.deadAt !== undefined && now - lastFrame.deadAt >= this.settings.drawDelay + 200) {
                        cell.views.delete(view);
                    }
                }
                if (cell.views.size === 0) {
                    collection.delete(id);
                }
            }
        }
    }
    // ===== MÉTODOS PRIVADOS =====
    // Mescla conjuntos de câmeras próximas
    mergeCameraSets(cameras, sets, now) {
        for (const [view, vision] of this.views) {
            const set = sets.get(view);
            const camera = cameras.get(view);
            if (camera.weight <= 0 || now - vision.used > 20000)
                continue; // Não mesclar abas inativas
            const x = camera.sumX / camera.weight;
            const y = camera.sumY / camera.weight;
            const width = 1920 / 2 / camera.scale;
            const height = 1080 / 2 / camera.scale;
            for (const [otherView, otherVision] of this.views) {
                const otherSet = sets.get(otherView);
                if (set === otherSet || now - otherVision.used > 20000)
                    continue;
                const otherCamera = cameras.get(otherView);
                if (otherCamera.weight <= 0)
                    continue;
                const otherX = otherCamera.sumX / otherCamera.weight;
                const otherY = otherCamera.sumY / otherCamera.weight;
                const otherWidth = 1920 / 2 / otherCamera.scale;
                const otherHeight = 1080 / 2 / otherCamera.scale;
                // Limite de proximidade baseado na massa de ambas as abas
                const threshold = 1000 + Math.min(camera.weight / 100 / 25, otherCamera.weight / 100 / 25);
                if (Math.abs(x - otherX) <= width + otherWidth + threshold && Math.abs(y - otherY) <= height + otherHeight + threshold) {
                    // Mesclar conjuntos disjuntos
                    for (const connectedView of otherSet) {
                        set.add(connectedView);
                        sets.set(connectedView, set);
                    }
                }
            }
        }
    }
    // Atualiza câmeras mescladas
    updateMergedCameras(cameras, sets, now) {
        const computed = new Set();
        for (const set of sets.values()) {
            if (computed.has(set))
                continue;
            let mass = 0;
            let sumX = 0;
            let sumY = 0;
            let weight = 0;
            if (this.settings.mergeCamera) {
                for (const view of set) {
                    const camera = cameras.get(view);
                    mass += camera.mass;
                    sumX += camera.sumX;
                    sumY += camera.sumY;
                    weight += camera.weight;
                }
            }
            for (const view of set) {
                const vision = this.views.get(view);
                if (!this.settings.mergeCamera) {
                    const camera = cameras.get(view);
                    ({ mass, sumX, sumY, weight } = camera);
                }
                this.updateSingleCameraPosition(vision, mass, sumX, sumY, weight, set.size > 1, now);
            }
            computed.add(set);
        }
    }
    // Atualiza posição de uma única câmera
    updateSingleCameraPosition(vision, mass, sumX, sumY, weight, isMerged, now) {
        let xyFactor;
        if (weight <= 0) {
            xyFactor = 20;
        }
        else if (this.settings.cameraMovement === "instant") {
            xyFactor = 1;
        }
        else {
            // Movimento de câmera suave após spawnar
            const aliveFor = (performance.now() - vision.spawned) / 1000;
            const a = Math.max(0, Math.min(1, (aliveFor - 0.3) / 0.3));
            const base = this.settings.cameraSpawnAnimation ? 2 : 1;
            xyFactor = Math.min(this.settings.cameraSmoothness, base * (1 - a) + this.settings.cameraSmoothness * a);
        }
        if (weight > 0) {
            vision.camera.tx = sumX / weight;
            vision.camera.ty = sumY / weight;
            let scale;
            if (this.settings.camera === "default") {
                scale = Math.pow(Math.min(64 / Math.sqrt(mass), 1), 0.4);
            }
            else {
                scale = Math.pow(Math.min(64 / Math.sqrt(100 * mass), 1), 0.4);
            }
            vision.camera.tscale = this.settings.autoZoom ? scale : 0.25;
        }
        const dt = (now - vision.camera.updated) / 1000;
        vision.camera.x = this.exponentialEase(vision.camera.x, vision.camera.tx, xyFactor, dt);
        vision.camera.y = this.exponentialEase(vision.camera.y, vision.camera.ty, xyFactor, dt);
        vision.camera.scale = this.exponentialEase(vision.camera.scale, vision.camera.tscale, 9, dt);
        vision.camera.merged = isMerged;
        vision.camera.updated = now;
    }
    // Implementa sincronização perfeita entre visualizações (versão simplificada)
    performFlawlessSynchronization(indices, now) {
        // Versão simplificada da sincronização perfeita
        // Em uma implementação real, isso envolveria algoritmos complexos de grafos bipartidos
        let i = 0;
        for (const view of this.views.keys()) {
            indices[i++] = indices[view] = 0; // Por simplicidade, usar sempre índice 0
        }
        this.wasFlawlessSynchronized = true;
    }
    // Implementa sincronização usando frames mais recentes
    performLatestSynchronization(indices) {
        let i = 0;
        for (const view of this.views.keys()) {
            indices[i++] = indices[view] = 0;
        }
        this.wasFlawlessSynchronized = false;
    }
    // Mescla frames baseado nos índices encontrados
    mergeFrames(indices, now) {
        for (const collection of [this.cells, this.pellets]) {
            for (const cell of collection.values()) {
                // Encontra frame modelo (versão simplificada)
                let modelFrame;
                for (const [view, record] of cell.views) {
                    const frame = record.frames[indices[view] || 0];
                    if (frame && !modelFrame) {
                        modelFrame = frame;
                    }
                }
                if (modelFrame) {
                    cell.model = modelFrame;
                }
            }
        }
        // Atualiza frames mesclados
        this.updateMergedFrames(now);
    }
    // Atualiza frames mesclados para todas as células
    updateMergedFrames(now) {
        var _a;
        for (const collection of [this.cells, this.pellets]) {
            for (const cell of collection.values()) {
                const { model, merged } = cell;
                if (!model) {
                    cell.merged = undefined;
                    continue;
                }
                if (!merged || (merged.deadAt !== undefined && model.deadAt === undefined)) {
                    // Cria novo frame mesclado
                    cell.merged = {
                        nx: model.nx,
                        ny: model.ny,
                        nr: model.nr,
                        born: now,
                        deadAt: model.deadAt !== undefined ? now : undefined,
                        deadTo: model.deadTo,
                        ox: model.nx,
                        oy: model.ny,
                        or: model.nr,
                        jr: model.nr,
                        a: 0,
                        updated: now,
                    };
                }
                else {
                    // Atualiza frame mesclado existente
                    if (merged.deadAt === undefined &&
                        (model.deadAt !== undefined || model.nx !== merged.nx || model.ny !== merged.ny || model.nr !== merged.nr)) {
                        const isPellet = collection === this.pellets;
                        const xyr = this.calculatePosition(merged, merged, undefined, undefined, isPellet, now);
                        merged.ox = xyr.x;
                        merged.oy = xyr.y;
                        merged.or = xyr.r;
                        merged.jr = xyr.jr;
                        merged.a = xyr.a;
                        merged.updated = now;
                    }
                    merged.nx = model.nx;
                    merged.ny = model.ny;
                    merged.nr = model.nr;
                    merged.deadAt = model.deadAt !== undefined ? (_a = merged.deadAt) !== null && _a !== void 0 ? _a : now : undefined;
                    merged.deadTo = model.deadTo;
                }
            }
        }
    }
    // Remove histórico de frames
    cleanupFrameHistory() {
        for (const collection of [this.cells, this.pellets]) {
            for (const cell of collection.values()) {
                for (const record of cell.views.values()) {
                    // Remove frames antigos, mantendo apenas o atual
                    while (record.frames.length > 1) {
                        record.frames.pop();
                    }
                }
            }
        }
    }
    // Reset do estado de sincronização
    resetSynchronization() {
        this.disagreementStart = undefined;
        this.disagreementAt = undefined;
        this.synchronized = false;
        this.wasFlawlessSynchronized = false;
    }
    // Atualiza estado de sincronização
    updateSynchronizationState(now) {
        this.disagreementStart = undefined;
        // Se houve desacordo, espera um tempo antes de reativar sincronização
        if (this.disagreementAt === undefined || now - this.disagreementAt > 1000) {
            this.synchronized = true;
        }
    }
    // Utilitário para easing exponencial
    exponentialEase(current, target, factor, deltaTime) {
        return current + (target - current) * (1 - Math.exp(-factor * deltaTime));
    }
    // ===== GETTERS PÚBLICOS =====
    get gameStats() {
        return Object.assign({}, this.stats);
    }
    get currentView() {
        return this.selected;
    }
    get allViews() {
        return this.views;
    }
    get isSynchronized() {
        return this.synchronized;
    }
    get cellCount() {
        return this.cells.size;
    }
    get pelletCount() {
        return this.pellets.size;
    }
}
class UIManager {
    constructor() {
        this.isInitialized = false;
        this.keyBindManager = KeyBindManager.getInstance();
    }
    static getInstance() {
        if (!UIManager.instance)
            UIManager.instance = new UIManager();
        return UIManager.instance;
    }
    initialize() {
        if (this.isInitialized)
            return true;
        this.keyBindManager.register({
            key: "F1",
            handler: () => alert("Ajuda"),
            description: "Mostra a ajuda",
        });
        // Keybind para mostrar status do WebSocket
        this.keyBindManager.register({
            key: "F2",
            handler: () => {
                const networkManager = NetworkManager.getInstance();
                const connections = networkManager.getAllConnectionsInfo();
                console.log("🔍 Informações das conexões:", connections);
                const recent = networkManager.getRecentMessages(30);
                console.log("🕒 Mensagens dos últimos 30 segundos:", recent);
                const stats = networkManager.getNetworkStats();
                console.log("📊 Estatísticas de rede:", stats);
            },
            description: "Mostra informações da rede",
        });
        this.isInitialized = true;
        return true;
    }
    destroy() {
        this.isInitialized = false;
    }
}
// ===== APLICAÇÃO PRINCIPAL ESSENCIAL =====
class ScriptApplication {
    constructor() {
        this.isInitialized = false;
        this.settingsStore = SettingsStore.getInstance();
        this.world = World.getInstance();
        this.uiManager = UIManager.getInstance();
        this.networkManager = NetworkManager.getInstance();
    }
    static getInstance() {
        if (!ScriptApplication.instance)
            ScriptApplication.instance = new ScriptApplication();
        return ScriptApplication.instance;
    }
    async initialize() {
        if (this.isInitialized)
            return true;
        if (document.readyState === "loading") {
            await new Promise(resolve => document.addEventListener("DOMContentLoaded", resolve));
        }
        this.uiManager.initialize();
        this.settingsStore.setSetting("appVersion", CONFIG.VERSION);
        console.log("🚀 Script inicializado com gerenciamento avançado de rede");
        console.log("ℹ️  Pressione F2 para ver estatísticas de rede");
        console.log("🌐 NetworkManager ativo com suporte a múltiplas conexões");
        this.isInitialized = true;
        return true;
    }
}
// ===== INICIALIZAÇÃO AUTOMÁTICA =====
// // Instancia o mundo do jogo como singleton
// const world = World.getInstance();
// Inicializa a aplicação principal
ScriptApplication.getInstance().initialize();
