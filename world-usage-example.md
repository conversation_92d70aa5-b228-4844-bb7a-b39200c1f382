# Exemplo de Uso da Classe World

A classe `World` representa uma versão refatorada e otimizada do sistema de mundo do jogo, implementada com padrões de código limpo e comentários em português.

## Características Principais

### 🏗️ Arquitetura

-   **Pa<PERSON><PERSON> Singleton**: Garante uma única instância da classe World
-   **Tipagem TypeScript**: Interfaces bem definidas para todos os dados
-   **Encapsulamento**: Métodos privados para lógica interna, públicos para API
-   **Separação de Responsabilidades**: Cada método tem uma função específica

### 🎮 Funcionalidades

#### 1. **Gerenciamento de Visualizações**

```typescript
// Criar uma nova visualização
const primaryView = world.createVision(world.currentView);

// Verificar se há células vivas
const isPlayerAlive = world.isAlive();

// Obter todas as visualizações ativas
const allViews = world.allViews;
```

#### 2. **Sistema de Câmeras**

```typescript
// Calcular dados de câmera para uma visualização específica
const cameraData = world.calculateSingleCamera(viewSymbol, undefined, 2, performance.now());

// Atualizar posições de todas as câmeras
world.updateCameras();
```

#### 3. **Sincronização entre Abas**

```typescript
// Sincronizar frames entre diferentes visualizações
world.synchronizeViews();

// Verificar se está sincronizado
const isSynced = world.isSynchronized;
```

#### 4. **Cálculo de Posições**

```typescript
// Calcular posição interpolada de uma célula
const position = world.calculatePosition(frame, interpolation, undefined, undefined, false);
console.log(`Posição: X=${position.x}, Y=${position.y}, Raio=${position.r}`);
```

#### 5. **Pontuação e Estatísticas**

```typescript
// Calcular pontuação para uma visualização
const score = world.calculateScore(viewSymbol);

// Obter estatísticas do jogo
const stats = world.gameStats;
console.log(`Maior pontuação: ${stats.highestScore}`);
```

#### 6. **Limpeza de Dados**

```typescript
// Remover células mortas e invisíveis (chamado automaticamente)
world.cleanupDeadCells();

// Verificar contadores
console.log(`Células: ${world.cellCount}, Pellets: ${world.pelletCount}`);
```

## Melhorias Implementadas

### 🔧 **Código Limpo**

-   **Nomes Descritivos**: Métodos e variáveis com nomes claros em português
-   **Funções Pequenas**: Cada método tem uma responsabilidade específica
-   **Comentários Úteis**: Explicações em português sem JSDoc desnecessário
-   **Organização Lógica**: Métodos agrupados por funcionalidade

### ⚡ **Otimizações**

-   **Caching Inteligente**: Evita recálculos desnecessários
-   **Cleanup Automático**: Remove dados antigos periodicamente
-   **Lazy Loading**: Cria visualizações apenas quando necessário
-   **Pooling de Objetos**: Reutiliza estruturas de dados quando possível

### 🛡️ **Robustez**

-   **Validação de Tipos**: TypeScript garante tipos corretos
-   **Tratamento de Erros**: Verifica dados antes de processar
-   **Estados Consistentes**: Sincronização confiável entre abas
-   **Fallbacks**: Comportamento gracioso quando dados estão ausentes

### 📊 **Monitoramento**

-   **Métricas Expostas**: Contadores de células e pellets
-   **Estado Observável**: Getters para verificar estado interno
-   **Debug Info**: Fácil acesso a informações de debug

## Diferenças da Implementação Original

| Aspecto          | Original              | Nova Classe            |
| ---------------- | --------------------- | ---------------------- |
| Padrão           | IIFE com objeto       | Classe Singleton       |
| Tipagem          | JSDoc                 | TypeScript nativo      |
| Organização      | Função monolítica     | Métodos especializados |
| Encapsulamento   | Propriedades expostas | API controlada         |
| Comentários      | Inglês + JSDoc        | Português claro        |
| Manutenibilidade | Baixa                 | Alta                   |
| Performance      | Boa                   | Otimizada              |
| Testabilidade    | Difícil               | Fácil                  |

## Uso Recomendado

```typescript
// Inicialização (feita automaticamente)
const world = World.getInstance();

// Em um loop de jogo típico:
function gameLoop() {
	// Atualizar câmeras
	world.updateCameras();

	// Sincronizar visualizações
	world.synchronizeViews();

	// Limpar dados antigos
	world.cleanupDeadCells();

	// Verificar estado do jogo
	if (!world.isAlive()) {
		console.log("Jogador morreu!");
	}

	// Calcular pontuação atual
	const currentScore = world.calculateScore(world.currentView);
	updateUI(currentScore);
}

// Função para atualizar UI
function updateUI(score: number) {
	const stats = world.gameStats;
	document.getElementById("score").textContent = score.toString();
	document.getElementById("highScore").textContent = stats.highestScore.toString();
	document.getElementById("cellCount").textContent = world.cellCount.toString();
}
```

A nova implementação mantém todas as funcionalidades da versão original, mas com melhor organização, performance e manutenibilidade.
